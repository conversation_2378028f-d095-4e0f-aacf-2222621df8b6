-- ========================================
-- CORE FILES & LOGS
-- ========================================
CREATE TABLE files (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  type ENUM('boh_purchase','bi_sales','bank_statement','email_settlement','qpon_statement'),
  original_name VARCHAR(255),
  storage_path VARCHAR(255),
  size_bytes BIGINT UNSIGNED,
  rows_detected INT UNSIGNED DEFAULT 0,
  status ENUM('uploaded','parsed','validated','committed','failed'),
  meta JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL,
  deleted_at TIMESTAMP NULL
);

CREATE TABLE raw_rows (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  file_id BIGINT,
  row_no INT UNSIGNED,
  row_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(file_id,row_no),
  FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE
);

CREATE TABLE audit_logs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  actor_id BIGINT NULL,
  action VARCHAR(100),
  subject_type VARCHAR(100),
  subject_id BIGINT NULL,
  before_state JSON NULL,
  after_state JSON NULL,
  message TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX(subject_type,subject_id),
  INDEX(created_at)
);

-- ========================================
-- PURCHASE (BOH)
-- ========================================
CREATE TABLE purchase_docs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  file_id BIGINT,
  doc_type ENUM('po','gr','invoice'),
  doc_no VARCHAR(80),
  doc_date DATE,
  supplier_code VARCHAR(80),
  warehouse_code VARCHAR(80),
  currency CHAR(3) DEFAULT 'IDR',
  subtotal DECIMAL(18,2),
  tax_total DECIMAL(18,2),
  grand_total DECIMAL(18,2),
  status ENUM('staged','posted','failed'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX(doc_no),
  FOREIGN KEY (file_id) REFERENCES files(id)
);

CREATE TABLE purchase_items (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  purchase_doc_id BIGINT,
  line_no INT,
  item_code VARCHAR(80),
  item_name_src VARCHAR(255),
  uom VARCHAR(20),
  qty DECIMAL(18,4),
  unit_price DECIMAL(18,4),
  line_total DECIMAL(18,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (purchase_doc_id) REFERENCES purchase_docs(id) ON DELETE CASCADE
);

-- ========================================
-- SALES (POS/BI)
-- ========================================
CREATE TABLE sales_docs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  file_id BIGINT,
  channel_code VARCHAR(40),
  sales_date DATE,
  gross_amount DECIMAL(18,2),
  fee_amount DECIMAL(18,2),
  ads_amount DECIMAL(18,2),
  net_amount DECIMAL(18,2),
  qpon_adjustment DECIMAL(18,2) DEFAULT 0,
  bank_in_amount DECIMAL(18,2) DEFAULT 0,
  delta_amount DECIMAL(18,2) DEFAULT 0,
  status ENUM('staged','matched','wait_qpon','diff','posted','failed'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX(channel_code,sales_date),
  FOREIGN KEY (file_id) REFERENCES files(id)
);

CREATE TABLE sales_items (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  sales_doc_id BIGINT,
  line_no INT,
  item_code VARCHAR(80),
  item_name_src VARCHAR(255),
  qty DECIMAL(18,4),
  gross_amount DECIMAL(18,2),
  net_amount DECIMAL(18,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (sales_doc_id) REFERENCES sales_docs(id) ON DELETE CASCADE
);

-- ========================================
-- BANK
-- ========================================
CREATE TABLE bank_rows (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  file_id BIGINT,
  txn_date DATE,
  description VARCHAR(255),
  descriptor_norm VARCHAR(255),
  debit DECIMAL(18,2),
  credit DECIMAL(18,2),
  balance DECIMAL(18,2),
  channel_guess VARCHAR(40),
  match_key VARCHAR(120),
  matched_sales_doc_id BIGINT NULL,
  status ENUM('unmatched','matched','ignored'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX(txn_date),
  INDEX(description),
  FOREIGN KEY (file_id) REFERENCES files(id)
);

-- ========================================
-- QPON / PROMO
-- ========================================
CREATE TABLE promo_redemptions (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  business_date DATE,
  branch_code VARCHAR(40),
  channel_code VARCHAR(40),
  amount_discount DECIMAL(18,2),
  orders INT,
  source ENUM('POS','EMAIL','MANUAL_REPORT'),
  status ENUM('pending','confirmed','rejected'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE qpon_statements (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  period_start DATE,
  period_end DATE,
  total_discount_reported DECIMAL(18,2),
  total_reimbursed DECIMAL(18,2),
  file_id BIGINT,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (file_id) REFERENCES files(id)
);

-- ========================================
-- RECONCILIATION
-- ========================================
CREATE TABLE reason_codes (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(40) UNIQUE,
  description VARCHAR(255)
);

CREATE TABLE recon_matches (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  sales_doc_id BIGINT,
  bank_row_id BIGINT,
  match_level ENUM('exact','amount_only','fuzzy'),
  delta_amount DECIMAL(18,2),
  reason_code VARCHAR(40),
  status ENUM('matched','difference','pending'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (sales_doc_id) REFERENCES sales_docs(id),
  FOREIGN KEY (bank_row_id) REFERENCES bank_rows(id)
);

CREATE TABLE recon_exceptions (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  recon_match_id BIGINT,
  severity ENUM('high','medium','low'),
  action VARCHAR(80),
  status ENUM('open','in_progress','resolved'),
  assignee VARCHAR(80),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (recon_match_id) REFERENCES recon_matches(id)
);
