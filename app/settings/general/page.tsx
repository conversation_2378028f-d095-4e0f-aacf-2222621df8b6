import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Settings, Save, RefreshCw, Database, Globe, Shield, Clock } from "lucide-react"

export default function GeneralSettingsPage() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">General Settings</h1>
              <p className="text-muted-foreground">Configure system-wide settings and preferences</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset to Defaults
              </Button>
              <Button size="sm">
                <Save className="w-4 h-4 mr-2" />
                Save All Changes
              </Button>
            </div>
          </div>

          {/* Settings */}
          <Tabs defaultValue="system" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="system">System</TabsTrigger>
              <TabsTrigger value="localization">Localization</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="backup">Backup</TabsTrigger>
            </TabsList>

            <TabsContent value="system" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>System Configuration</span>
                  </CardTitle>
                  <CardDescription>Basic system settings and preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>System Name</Label>
                      <Input defaultValue="Tomoro Bridging System" />
                    </div>
                    <div className="space-y-2">
                      <Label>Environment</Label>
                      <Select defaultValue="production">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="development">Development</SelectItem>
                          <SelectItem value="staging">Staging</SelectItem>
                          <SelectItem value="production">Production</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>System Description</Label>
                    <Textarea
                      defaultValue="Bridging system for Tomoro Coffee franchise operations - BEI and Lotte Mall Jakarta locations"
                      rows={3}
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Session Timeout (minutes)</Label>
                      <Input type="number" defaultValue="60" />
                    </div>
                    <div className="space-y-2">
                      <Label>Max File Upload Size (MB)</Label>
                      <Input type="number" defaultValue="50" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch id="maintenance-mode" />
                      <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="debug-mode" />
                      <Label htmlFor="debug-mode">Debug Mode</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="auto-backup" defaultChecked />
                      <Label htmlFor="auto-backup">Automatic Backups</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Database className="h-5 w-5" />
                    <span>Data Management</span>
                  </CardTitle>
                  <CardDescription>Configure data retention and cleanup policies</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label>Audit Log Retention (days)</Label>
                      <Input type="number" defaultValue="365" />
                    </div>
                    <div className="space-y-2">
                      <Label>File Retention (days)</Label>
                      <Input type="number" defaultValue="90" />
                    </div>
                    <div className="space-y-2">
                      <Label>Temp File Cleanup (hours)</Label>
                      <Input type="number" defaultValue="24" />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="auto-cleanup" defaultChecked />
                    <Label htmlFor="auto-cleanup">Automatic cleanup of old files</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="localization" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Globe className="h-5 w-5" />
                    <span>Localization Settings</span>
                  </CardTitle>
                  <CardDescription>Configure language, timezone, and regional settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Default Language</Label>
                      <Select defaultValue="id">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="id">Bahasa Indonesia</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Timezone</Label>
                      <Select defaultValue="asia-jakarta">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="asia-jakarta">Asia/Jakarta (WIB)</SelectItem>
                          <SelectItem value="utc">UTC</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Date Format</Label>
                      <Select defaultValue="dd-mm-yyyy">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dd-mm-yyyy">DD-MM-YYYY</SelectItem>
                          <SelectItem value="mm-dd-yyyy">MM-DD-YYYY</SelectItem>
                          <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Time Format</Label>
                      <Select defaultValue="24h">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="24h">24 Hour</SelectItem>
                          <SelectItem value="12h">12 Hour (AM/PM)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Currency</Label>
                      <Select defaultValue="idr">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="idr">Indonesian Rupiah (IDR)</SelectItem>
                          <SelectItem value="usd">US Dollar (USD)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Number Format</Label>
                      <Select defaultValue="id">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="id">1.234.567,89 (Indonesian)</SelectItem>
                          <SelectItem value="us">1,234,567.89 (US)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Security Settings</span>
                  </CardTitle>
                  <CardDescription>Configure security policies and authentication</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Password Min Length</Label>
                      <Input type="number" defaultValue="8" />
                    </div>
                    <div className="space-y-2">
                      <Label>Password Expiry (days)</Label>
                      <Input type="number" defaultValue="90" />
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Max Login Attempts</Label>
                      <Input type="number" defaultValue="5" />
                    </div>
                    <div className="space-y-2">
                      <Label>Account Lockout Duration (minutes)</Label>
                      <Input type="number" defaultValue="30" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch id="require-complex-password" defaultChecked />
                      <Label htmlFor="require-complex-password">Require complex passwords</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="two-factor-auth" />
                      <Label htmlFor="two-factor-auth">Enable two-factor authentication</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="ip-whitelist" />
                      <Label htmlFor="ip-whitelist">Enable IP address whitelist</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="audit-all-actions" defaultChecked />
                      <Label htmlFor="audit-all-actions">Audit all user actions</Label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Allowed IP Addresses (one per line)</Label>
                    <Textarea placeholder="***********/24&#10;10.0.0.0/8" rows={4} />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="backup" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Database className="h-5 w-5" />
                    <span>Backup & Recovery</span>
                  </CardTitle>
                  <CardDescription>Configure automatic backups and recovery options</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Backup Frequency</Label>
                      <Select defaultValue="daily">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">Hourly</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Backup Time</Label>
                      <Input type="time" defaultValue="02:00" />
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Retention Period (days)</Label>
                      <Input type="number" defaultValue="30" />
                    </div>
                    <div className="space-y-2">
                      <Label>Max Backup Files</Label>
                      <Input type="number" defaultValue="10" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Backup Location</Label>
                    <Input defaultValue="/backups/tomoro-bridging/" />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch id="auto-backup-enabled" defaultChecked />
                      <Label htmlFor="auto-backup-enabled">Enable automatic backups</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="compress-backups" defaultChecked />
                      <Label htmlFor="compress-backups">Compress backup files</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="encrypt-backups" />
                      <Label htmlFor="encrypt-backups">Encrypt backup files</Label>
                    </div>
                  </div>

                  <Alert>
                    <Clock className="h-4 w-4" />
                    <AlertDescription>
                      Last backup: 2024-12-15 02:00:00 (Success) - Next backup: 2024-12-16 02:00:00
                    </AlertDescription>
                  </Alert>

                  <div className="flex space-x-2">
                    <Button variant="outline">
                      <Database className="w-4 h-4 mr-2" />
                      Create Backup Now
                    </Button>
                    <Button variant="outline">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Restore from Backup
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
