import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, UserPlus, Shield, Edit, Trash2, Key, Mail } from "lucide-react"

const users = [
  {
    id: "1",
    name: "Ad<PERSON>",
    email: "<EMAIL>",
    role: "IT/Administrator",
    status: "active",
    lastLogin: "2024-12-15 14:30:00",
    permissions: ["all"],
    avatar: null,
  },
  {
    id: "2",
    name: "FAT PSM",
    email: "<EMAIL>",
    role: "FAT PSM",
    status: "active",
    lastLogin: "2024-12-15 14:25:00",
    permissions: ["purchase", "sales", "master_data", "accurate_posting"],
    avatar: null,
  },
  {
    id: "3",
    name: "OPS BEI",
    email: "<EMAIL>",
    role: "OPS",
    status: "active",
    lastLogin: "2024-12-15 13:45:00",
    permissions: ["purchase_view", "boh_confirm"],
    avatar: null,
  },
  {
    id: "4",
    name: "OPS Lotte",
    email: "<EMAIL>",
    role: "OPS",
    status: "active",
    lastLogin: "2024-12-15 12:30:00",
    permissions: ["purchase_view", "boh_confirm"],
    avatar: null,
  },
  {
    id: "5",
    name: "Finance Manager",
    email: "<EMAIL>",
    role: "FAT PSM",
    status: "inactive",
    lastLogin: "2024-12-10 16:00:00",
    permissions: ["purchase", "sales", "reports"],
    avatar: null,
  },
]

const roles = [
  {
    name: "IT/Administrator",
    description: "Full system access and configuration",
    permissions: ["User Management", "System Configuration", "Integration Management", "Audit Logs", "All Modules"],
    userCount: 1,
  },
  {
    name: "FAT PSM",
    description: "Finance & Accounting operations",
    permissions: [
      "Purchase Management",
      "Sales & Reconciliation",
      "Master Data Mapping",
      "Accurate Posting",
      "Reports & Analytics",
    ],
    userCount: 2,
  },
  {
    name: "OPS",
    description: "Operational tasks and confirmations",
    permissions: ["BOH Confirmation", "Purchase View Only", "Receiving Management"],
    userCount: 2,
  },
]

export default function UsersPage() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
              <p className="text-muted-foreground">Manage users, roles, and permissions</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <UserPlus className="w-4 h-4 mr-2" />
                Add User
              </Button>
              <Button size="sm">
                <Shield className="w-4 h-4 mr-2" />
                Manage Roles
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">registered users</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Users className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4</div>
                <p className="text-xs text-muted-foreground">currently active</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">User Roles</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">defined roles</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Online Now</CardTitle>
                <Users className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">users online</p>
              </CardContent>
            </Card>
          </div>

          {/* User Management */}
          <Tabs defaultValue="users" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
            </TabsList>

            <TabsContent value="users" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>User Accounts</CardTitle>
                  <CardDescription>Manage user accounts and their access</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {users.map((user) => (
                      <div key={user.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-4">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={user.avatar || undefined} />
                              <AvatarFallback>
                                {user.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="font-medium">{user.name}</h4>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={user.status === "active" ? "default" : "secondary"}>{user.status}</Badge>
                            <Badge variant="outline">{user.role}</Badge>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-destructive">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-3 text-sm">
                          <div>
                            <Label className="text-xs text-muted-foreground">Last Login</Label>
                            <p className="font-medium">{user.lastLogin}</p>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Permissions</Label>
                            <p className="font-medium">{user.permissions.length} permissions</p>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Account Status</Label>
                            <p
                              className={`font-medium ${user.status === "active" ? "text-green-600" : "text-gray-600"}`}
                            >
                              {user.status === "active" ? "Active" : "Inactive"}
                            </p>
                          </div>
                        </div>

                        {/* User Configuration */}
                        <div className="mt-4 pt-4 border-t">
                          <div className="grid gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                              <Label className="text-sm">Role</Label>
                              <Select defaultValue={user.role.toLowerCase().replace(/[^a-z]/g, "_")}>
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="it_administrator">IT/Administrator</SelectItem>
                                  <SelectItem value="fat_psm">FAT PSM</SelectItem>
                                  <SelectItem value="ops">OPS</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm">Status</Label>
                              <Select defaultValue={user.status}>
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="active">Active</SelectItem>
                                  <SelectItem value="inactive">Inactive</SelectItem>
                                  <SelectItem value="suspended">Suspended</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm">Actions</Label>
                              <div className="flex space-x-1">
                                <Button variant="outline" size="sm" className="h-8 bg-transparent">
                                  <Key className="w-3 h-3 mr-1" />
                                  Reset Password
                                </Button>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm">Notifications</Label>
                              <div className="flex space-x-1">
                                <Button variant="outline" size="sm" className="h-8 bg-transparent">
                                  <Mail className="w-3 h-3 mr-1" />
                                  Send Email
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="roles" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Roles & Permissions</CardTitle>
                  <CardDescription>Define user roles and their permissions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {roles.map((role, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-lg">{role.name}</h4>
                            <p className="text-sm text-muted-foreground">{role.description}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{role.userCount} users</Badge>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div>
                          <Label className="text-sm font-medium">Permissions</Label>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {role.permissions.map((permission, permIndex) => (
                              <Badge key={permIndex} variant="secondary" className="text-xs">
                                {permission}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Permission Matrix */}
                        <div className="mt-4 pt-4 border-t">
                          <Label className="text-sm font-medium">Detailed Permissions</Label>
                          <div className="grid gap-2 md:grid-cols-2 mt-2">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">Dashboard Access</Label>
                                <Switch checked={role.name !== "OPS"} />
                              </div>
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">Purchase Management</Label>
                                <Switch checked={role.name !== "OPS"} />
                              </div>
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">Sales & Reconciliation</Label>
                                <Switch checked={role.name === "FAT PSM" || role.name === "IT/Administrator"} />
                              </div>
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">Master Data Mapping</Label>
                                <Switch checked={role.name === "FAT PSM" || role.name === "IT/Administrator"} />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">Integration Settings</Label>
                                <Switch checked={role.name === "IT/Administrator"} />
                              </div>
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">User Management</Label>
                                <Switch checked={role.name === "IT/Administrator"} />
                              </div>
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">Audit Logs</Label>
                                <Switch checked={role.name === "IT/Administrator"} />
                              </div>
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">System Settings</Label>
                                <Switch checked={role.name === "IT/Administrator"} />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
