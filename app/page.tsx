import { Sidebar } from "@/components/sidebar"
import { DashboardStats } from "@/components/dashboard-stats"
import { QuickActions } from "@/components/quick-actions"
import { RecentActivity } from "@/components/recent-activity"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChannelDifferenceChart } from "@/components/charts/channel-difference-chart"
import { SuccessRateChart } from "@/components/charts/success-rate-chart"

export default function Dashboard() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
              <p className="text-muted-foreground">Ringkasan status operasional Tomoro Bridging System</p>
            </div>
            <div className="text-sm text-muted-foreground">Last updated: {new Date().toLocaleString("id-ID")}</div>
          </div>

          {/* Stats Cards */}
          <DashboardStats />

          {/* Quick Actions */}
          <QuickActions />

          {/* Charts */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Selisih per Channel (7 Hari)</CardTitle>
                <CardDescription>Tracking selisih rekonsiliasi per channel delivery</CardDescription>
              </CardHeader>
              <CardContent>
                <ChannelDifferenceChart />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Success Rate Trend</CardTitle>
                <CardDescription>Persentase keberhasilan proses bridging harian</CardDescription>
              </CardHeader>
              <CardContent>
                <SuccessRateChart />
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <RecentActivity />
        </div>
      </main>
    </div>
  )
}
