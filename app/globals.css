@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens for Tomoro Coffee brand - orange and white theme */
  --background: oklch(1 0 0); /* Clean white background */
  --foreground: oklch(0.35 0 0); /* Dark gray for text */
  --card: oklch(0.98 0 0); /* Light gray for cards */
  --card-foreground: oklch(0.35 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.35 0 0);
  --primary: oklch(0.65 0.2 25); /* Tomoro Coffee orange */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.95 0.02 25); /* Light orange accent */
  --secondary-foreground: oklch(0.35 0 0);
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.55 0 0);
  --accent: oklch(0.75 0.15 30); /* Warm orange for highlights */
  --accent-foreground: oklch(0.35 0 0);
  --destructive: oklch(0.55 0.2 15); /* Red-orange for errors */
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(1 0 0);
  --ring: oklch(0.65 0.2 25 / 0.5);
  --chart-1: oklch(0.65 0.2 25); /* Orange */
  --chart-2: oklch(0.75 0.15 30); /* Light orange */
  --chart-3: oklch(0.55 0.25 20); /* Dark orange */
  --chart-4: oklch(0.85 0.1 35); /* Cream orange */
  --chart-5: oklch(0.45 0.15 15); /* Brown orange */
  --radius: 0.5rem;
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.35 0 0);
  --sidebar-primary: oklch(0.65 0.2 25); /* Tomoro orange */
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.96 0 0);
  --sidebar-accent-foreground: oklch(0.35 0 0);
  --sidebar-border: oklch(0.9 0 0);
  --sidebar-ring: oklch(0.65 0.2 25 / 0.5);
}

.dark {
  /* Updated dark theme colors to complement orange brand */
  --background: oklch(0.15 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.18 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.15 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.75 0.2 25); /* Brighter orange for dark mode */
  --primary-foreground: oklch(0.15 0 0);
  --secondary: oklch(0.25 0 0);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.25 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.75 0.15 30); /* Warm orange accent */
  --accent-foreground: oklch(0.15 0 0);
  --destructive: oklch(0.6 0.2 15);
  --destructive-foreground: oklch(0.95 0 0);
  --border: oklch(0.25 0 0);
  --input: oklch(0.25 0 0);
  --ring: oklch(0.75 0.2 25 / 0.5);
  --chart-1: oklch(0.75 0.2 25);
  --chart-2: oklch(0.85 0.15 30);
  --chart-3: oklch(0.65 0.25 20);
  --chart-4: oklch(0.95 0.1 35);
  --chart-5: oklch(0.55 0.15 15);
  --sidebar: oklch(0.18 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.75 0.2 25);
  --sidebar-primary-foreground: oklch(0.15 0 0);
  --sidebar-accent: oklch(0.25 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.25 0 0);
  --sidebar-ring: oklch(0.75 0.2 25 / 0.5);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
