"use client"

import { Sidebar } from "@/components/sidebar"
import { MappingTable } from "@/components/mapping-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Package, Upload, Download, Zap } from "lucide-react"

const itemMappingData = [
  {
    id: "1",
    source: "ARABICA-PREMIUM-001",
    target: "Arabica Beans Premium - KG",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-15 10:30",
    updatedBy: "FAT PSM",
  },
  {
    id: "2",
    source: "MILK-FRESH-1L",
    target: "Fresh Milk 1L - PCS",
    status: "active" as const,
    confidence: 95,
    lastUpdated: "2024-03-15 10:25",
    updatedBy: "FAT PSM",
  },
  {
    id: "3",
    source: "SUGAR-BROWN-KG",
    target: "Brown Sugar - KG",
    status: "pending" as const,
    lastUpdated: "2024-03-15 09:15",
    updatedBy: "System",
  },
  {
    id: "4",
    source: "CUP-PAPER-8OZ",
    target: "Paper Cup 8oz - PCS",
    status: "active" as const,
    confidence: 90,
    lastUpdated: "2024-03-14 15:20",
    updatedBy: "FAT PSM",
  },
  {
    id: "5",
    source: "LID-PLASTIC-8OZ",
    target: "Plastic Lid 8oz - PCS",
    status: "inactive" as const,
    lastUpdated: "2024-03-13 11:45",
    updatedBy: "FAT PSM",
  },
]

const accurateItems = [
  { value: "arabica-premium-kg", label: "Arabica Beans Premium - KG" },
  { value: "fresh-milk-1l-pcs", label: "Fresh Milk 1L - PCS" },
  { value: "brown-sugar-kg", label: "Brown Sugar - KG" },
  { value: "paper-cup-8oz-pcs", label: "Paper Cup 8oz - PCS" },
  { value: "plastic-lid-8oz-pcs", label: "Plastic Lid 8oz - PCS" },
  { value: "robusta-beans-kg", label: "Robusta Beans - KG" },
  { value: "coconut-milk-1l-pcs", label: "Coconut Milk 1L - PCS" },
  { value: "white-sugar-kg", label: "White Sugar - KG" },
  { value: "paper-cup-12oz-pcs", label: "Paper Cup 12oz - PCS" },
  { value: "plastic-lid-12oz-pcs", label: "Plastic Lid 12oz - PCS" },
]

export default function ItemMapping() {
  const handleAdd = (item: any) => {
    console.log("Add item mapping:", item)
  }

  const handleEdit = (id: string, item: any) => {
    console.log("Edit item mapping:", id, item)
  }

  const handleDelete = (id: string) => {
    console.log("Delete item mapping:", id)
  }

  const handleAutoMapping = () => {
    console.log("Start auto mapping process")
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Item/Product Mapping</h1>
              <p className="text-muted-foreground">Mapping BOH SKU ke Accurate Item dengan unit dan konversi</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleAutoMapping}>
                <Zap className="w-4 h-4 mr-2" />
                Auto Mapping
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Import Items
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Mapping
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Items</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">mapped items</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">active mappings</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">needs review</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Auto Mapped</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">80%</div>
                <p className="text-xs text-muted-foreground">success rate</p>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Alert>
            <Package className="h-4 w-4" />
            <AlertDescription>
              Item mapping menghubungkan SKU dari BOH dengan master item di Accurate. Pastikan unit dan konversi sudah
              benar untuk menghindari kesalahan quantity dalam posting.
            </AlertDescription>
          </Alert>

          {/* Item Mapping Table */}
          <MappingTable
            title="BOH SKU to Accurate Item Mapping"
            data={itemMappingData}
            sourceLabel="BOH SKU"
            targetLabel="Accurate Item"
            targetOptions={accurateItems}
            onAdd={handleAdd}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />

          {/* Unit Conversion Rules */}
          <Card>
            <CardHeader>
              <CardTitle>Unit Conversion Rules</CardTitle>
              <CardDescription>Aturan konversi unit untuk setiap item</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Arabica Beans Premium</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>BOH Unit:</span>
                        <span className="font-medium">KG</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Accurate Unit:</span>
                        <span className="font-medium">KG</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Conversion:</span>
                        <span className="font-medium">1:1</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax:</span>
                        <span className="font-medium">PPN 11%</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Fresh Milk 1L</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>BOH Unit:</span>
                        <span className="font-medium">PCS</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Accurate Unit:</span>
                        <span className="font-medium">PCS</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Conversion:</span>
                        <span className="font-medium">1:1</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax:</span>
                        <span className="font-medium">PPN 11%</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Paper Cup 8oz</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>BOH Unit:</span>
                        <span className="font-medium">PCS</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Accurate Unit:</span>
                        <span className="font-medium">PCS</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Conversion:</span>
                        <span className="font-medium">1:1</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax:</span>
                        <span className="font-medium">PPN 11%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <p className="text-sm text-muted-foreground">
                    Unit conversion rules help ensure accurate quantity calculations during posting
                  </p>
                  <Button variant="outline" size="sm">
                    Edit Conversion Rules
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
