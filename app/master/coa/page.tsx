"use client"

import { Sidebar } from "@/components/sidebar"
import { MappingTable } from "@/components/mapping-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BookOpen, TrendingUp, ShoppingCart, Banknote, Upload, Download } from "lucide-react"

const coaMappingData = [
  {
    id: "1",
    source: "Arabica Beans Premium",
    target: "5-1100 - Persed<PERSON><PERSON>",
    status: "active" as const,
    confidence: 95,
    lastUpdated: "2024-03-15 10:30",
    updatedBy: "FAT PSM",
  },
  {
    id: "2",
    source: "Fresh Milk 1L",
    target: "5-1100 - <PERSON><PERSON><PERSON><PERSON>",
    status: "active" as const,
    confidence: 90,
    lastUpdated: "2024-03-15 10:25",
    updatedBy: "FAT PSM",
  },
  {
    id: "3",
    source: "Brown Sugar",
    target: "5-1100 - Persediaan <PERSON>",
    status: "pending" as const,
    lastUpdated: "2024-03-15 09:15",
    updatedBy: "System",
  },
]

const salesCoaData = [
  {
    id: "1",
    source: "GoFood Sales",
    target: "4-1100 - Penjualan GoFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-14 16:00",
    updatedBy: "FAT PSM",
  },
  {
    id: "2",
    source: "GrabFood Sales",
    target: "4-1200 - Penjualan GrabFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-14 16:00",
    updatedBy: "FAT PSM",
  },
  {
    id: "3",
    source: "ShopeeFood Sales",
    target: "4-1300 - Penjualan ShopeeFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-14 16:00",
    updatedBy: "FAT PSM",
  },
]

const expenseCoaData = [
  {
    id: "1",
    source: "GoFood Commission",
    target: "6-2100 - Beban Komisi GoFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-14 16:00",
    updatedBy: "FAT PSM",
  },
  {
    id: "2",
    source: "GrabFood Commission",
    target: "6-2200 - Beban Komisi GrabFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-14 16:00",
    updatedBy: "FAT PSM",
  },
  {
    id: "3",
    source: "Advertising Expense",
    target: "6-3100 - Beban Iklan Online",
    status: "active" as const,
    confidence: 95,
    lastUpdated: "2024-03-14 16:00",
    updatedBy: "FAT PSM",
  },
]

const coaOptions = [
  { value: "5-1100", label: "5-1100 - Persediaan Bahan Baku" },
  { value: "5-1200", label: "5-1200 - Persediaan Barang Jadi" },
  { value: "5-1300", label: "5-1300 - Persediaan Supplies" },
  { value: "4-1100", label: "4-1100 - Penjualan GoFood" },
  { value: "4-1200", label: "4-1200 - Penjualan GrabFood" },
  { value: "4-1300", label: "4-1300 - Penjualan ShopeeFood" },
  { value: "6-2100", label: "6-2100 - Beban Komisi GoFood" },
  { value: "6-2200", label: "6-2200 - Beban Komisi GrabFood" },
  { value: "6-3100", label: "6-3100 - Beban Iklan Online" },
  { value: "1-1100", label: "1-1100 - Kas Bank" },
  { value: "1-1200", label: "1-1200 - Selisih Bank" },
]

export default function COAMapping() {
  const handleAdd = (item: any) => {
    console.log("Add COA mapping:", item)
  }

  const handleEdit = (id: string, item: any) => {
    console.log("Edit COA mapping:", id, item)
  }

  const handleDelete = (id: string) => {
    console.log("Delete COA mapping:", id)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">COA Mapping</h1>
              <p className="text-muted-foreground">
                Mapping akun pembelian, COGS, sales, fee, ads, dan selisih bank ke Chart of Account
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Import COA
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Mapping
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Mappings</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">9</div>
                <p className="text-xs text-muted-foreground">active mappings</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Purchase COA</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">inventory accounts</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Sales COA</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">revenue accounts</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Expense COA</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">expense accounts</p>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Alert>
            <BookOpen className="h-4 w-4" />
            <AlertDescription>
              COA Mapping menentukan akun mana yang akan digunakan untuk setiap jenis transaksi. Pastikan mapping sudah
              sesuai dengan struktur akun di Accurate sebelum melakukan posting.
            </AlertDescription>
          </Alert>

          {/* COA Mapping Tabs */}
          <Tabs defaultValue="purchase" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="purchase">Purchase & Inventory</TabsTrigger>
              <TabsTrigger value="sales">Sales & Revenue</TabsTrigger>
              <TabsTrigger value="expense">Expense & Fees</TabsTrigger>
            </TabsList>

            <TabsContent value="purchase">
              <MappingTable
                title="Purchase & Inventory COA Mapping"
                data={coaMappingData}
                sourceLabel="Item/Product"
                targetLabel="COA Account"
                targetOptions={coaOptions.filter((opt) => opt.value.startsWith("5-"))}
                onAdd={handleAdd}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </TabsContent>

            <TabsContent value="sales">
              <MappingTable
                title="Sales & Revenue COA Mapping"
                data={salesCoaData}
                sourceLabel="Sales Channel"
                targetLabel="Revenue Account"
                targetOptions={coaOptions.filter((opt) => opt.value.startsWith("4-"))}
                onAdd={handleAdd}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </TabsContent>

            <TabsContent value="expense">
              <MappingTable
                title="Expense & Fees COA Mapping"
                data={expenseCoaData}
                sourceLabel="Expense Type"
                targetLabel="Expense Account"
                targetOptions={coaOptions.filter((opt) => opt.value.startsWith("6-") || opt.value.startsWith("1-"))}
                onAdd={handleAdd}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </TabsContent>
          </Tabs>

          {/* COA Structure Preview */}
          <Card>
            <CardHeader>
              <CardTitle>COA Structure Preview</CardTitle>
              <CardDescription>Preview struktur akun yang akan digunakan dalam posting</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="font-medium">Assets & Inventory</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between p-2 border rounded">
                      <span>1-1100 - Kas Bank</span>
                      <Badge variant="secondary">Bank Reconciliation</Badge>
                    </div>
                    <div className="flex justify-between p-2 border rounded">
                      <span>1-1200 - Selisih Bank</span>
                      <Badge variant="secondary">Variance Account</Badge>
                    </div>
                    <div className="flex justify-between p-2 border rounded">
                      <span>5-1100 - Persediaan Bahan Baku</span>
                      <Badge variant="secondary">Inventory</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Revenue & Expenses</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between p-2 border rounded">
                      <span>4-1100 - Penjualan GoFood</span>
                      <Badge variant="default">Revenue</Badge>
                    </div>
                    <div className="flex justify-between p-2 border rounded">
                      <span>6-2100 - Beban Komisi GoFood</span>
                      <Badge variant="destructive">Expense</Badge>
                    </div>
                    <div className="flex justify-between p-2 border rounded">
                      <span>6-3100 - Beban Iklan Online</span>
                      <Badge variant="destructive">Marketing</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
