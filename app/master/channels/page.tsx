"use client"
import { Sidebar } from "@/components/sidebar"
import { MappingTable } from "@/components/mapping-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Smartphone, Upload, Download, Settings } from "lucide-react"

const channelMappingData = [
  {
    id: "1",
    source: "GoFood",
    target: "GOFOOD-CHANNEL",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-15 10:30",
    updatedBy: "FAT PSM",
  },
  {
    id: "2",
    source: "GrabFood",
    target: "GRABFOOD-CHANNEL",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-15 10:25",
    updatedBy: "FAT PSM",
  },
  {
    id: "3",
    source: "ShopeeFood",
    target: "SHOPEEFOOD-CHANNEL",
    status: "active" as const,
    confidence: 95,
    lastUpdated: "2024-03-15 09:15",
    updatedBy: "FAT PSM",
  },
  {
    id: "4",
    source: "Offline",
    target: "OFFLINE-SALES",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-14 15:20",
    updatedBy: "FAT PSM",
  },
]

const accurateChannels = [
  { value: "gofood-channel", label: "GOFOOD-CHANNEL" },
  { value: "grabfood-channel", label: "GRABFOOD-CHANNEL" },
  { value: "shopeefood-channel", label: "SHOPEEFOOD-CHANNEL" },
  { value: "offline-sales", label: "OFFLINE-SALES" },
  { value: "traveloka-eats", label: "TRAVELOKA-EATS" },
  { value: "maxim-food", label: "MAXIM-FOOD" },
]

const channelSettings = [
  {
    channel: "GoFood",
    toleranceDays: 1,
    toleranceAmount: 50000,
    feePercentage: 20,
    adsPercentage: 0.5,
    payoutSchedule: "Daily",
  },
  {
    channel: "GrabFood",
    toleranceDays: 1,
    toleranceAmount: 50000,
    feePercentage: 20,
    adsPercentage: 1.5,
    payoutSchedule: "Daily",
  },
  {
    channel: "ShopeeFood",
    toleranceDays: 2,
    toleranceAmount: 100000,
    feePercentage: 15,
    adsPercentage: 2.0,
    payoutSchedule: "Weekly",
  },
]

export default function ChannelMapping() {
  const handleAdd = (item: any) => {
    console.log("Add channel mapping:", item)
  }

  const handleEdit = (id: string, item: any) => {
    console.log("Edit channel mapping:", id, item)
  }

  const handleDelete = (id: string) => {
    console.log("Delete channel mapping:", id)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Channel Mapping</h1>
              <p className="text-muted-foreground">
                Mapping channel delivery ke kode Accurate dengan toleransi dan aturan khusus
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Import Channels
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Mapping
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Channels</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4</div>
                <p className="text-xs text-muted-foreground">delivery channels</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Daily Payout</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2</div>
                <p className="text-xs text-muted-foreground">channels</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Weekly Payout</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">channel</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Commission</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">18.3%</div>
                <p className="text-xs text-muted-foreground">across channels</p>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Alert>
            <Smartphone className="h-4 w-4" />
            <AlertDescription>
              Channel mapping menentukan bagaimana setiap platform delivery diidentifikasi dan diproses. Setiap channel
              memiliki aturan toleransi dan jadwal payout yang berbeda.
            </AlertDescription>
          </Alert>

          {/* Channel Mapping Table */}
          <MappingTable
            title="BI Channel to Accurate Code Mapping"
            data={channelMappingData}
            sourceLabel="BI Channel"
            targetLabel="Accurate Code"
            targetOptions={accurateChannels}
            onAdd={handleAdd}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />

          {/* Channel Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Channel Settings & Rules</span>
              </CardTitle>
              <CardDescription>
                Konfigurasi toleransi tanggal, nominal, fee, dan jadwal payout per channel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {channelSettings.map((setting) => (
                  <div key={setting.channel} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-lg">{setting.channel}</h4>
                      <Badge variant="secondary">{setting.payoutSchedule} Payout</Badge>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Tolerance Settings</Label>
                        <div className="space-y-3 p-3 bg-muted/50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <Label htmlFor={`days-${setting.channel}`} className="text-xs">
                              Date Tolerance (days)
                            </Label>
                            <Input
                              id={`days-${setting.channel}`}
                              type="number"
                              value={setting.toleranceDays}
                              className="w-16 h-8 text-xs"
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <Label htmlFor={`amount-${setting.channel}`} className="text-xs">
                              Amount Tolerance (Rp)
                            </Label>
                            <Input
                              id={`amount-${setting.channel}`}
                              type="number"
                              value={setting.toleranceAmount}
                              className="w-20 h-8 text-xs"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Fee Structure</Label>
                        <div className="space-y-3 p-3 bg-muted/50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <Label htmlFor={`fee-${setting.channel}`} className="text-xs">
                              Commission (%)
                            </Label>
                            <Input
                              id={`fee-${setting.channel}`}
                              type="number"
                              value={setting.feePercentage}
                              className="w-16 h-8 text-xs"
                              step="0.1"
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <Label htmlFor={`ads-${setting.channel}`} className="text-xs">
                              Ads Fee (%)
                            </Label>
                            <Input
                              id={`ads-${setting.channel}`}
                              type="number"
                              value={setting.adsPercentage}
                              className="w-16 h-8 text-xs"
                              step="0.1"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Payout Schedule</Label>
                        <div className="p-3 bg-muted/50 rounded-lg">
                          <div className="text-center">
                            <div className="text-lg font-medium">{setting.payoutSchedule}</div>
                            <div className="text-xs text-muted-foreground">
                              {setting.payoutSchedule === "Daily" ? "T+1 settlement" : "Every Monday"}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end mt-4">
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-2" />
                        Update Settings
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
