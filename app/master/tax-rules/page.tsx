import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calculator, Percent, Save, Plus, Edit, Trash2 } from "lucide-react"

const taxRules = [
  {
    id: "1",
    name: "PPN Bahan Baku",
    type: "purchase",
    rate: 11,
    description: "PPN untuk pembelian bahan baku",
    applicableItems: ["Arabica Beans", "Fresh Milk", "Sugar"],
    status: "active",
  },
  {
    id: "2",
    name: "PPN Supplies",
    type: "purchase",
    rate: 11,
    description: "PPN untuk supplies dan packaging",
    applicableItems: ["Paper Cup", "Plastic Lid", "Napkins"],
    status: "active",
  },
  {
    id: "3",
    name: "Non-Taxable Sales",
    type: "sales",
    rate: 0,
    description: "Penjualan tidak kena pajak",
    applicableItems: ["All Food Items"],
    status: "active",
  },
]

const feeRules = [
  {
    id: "1",
    channel: "GoFood",
    commissionRate: 20,
    adsRate: 0.5,
    minimumFee: 1000,
    maximumFee: 50000,
    calculationMethod: "percentage",
    status: "active",
  },
  {
    id: "2",
    channel: "GrabFood",
    commissionRate: 20,
    adsRate: 1.5,
    minimumFee: 1000,
    maximumFee: 50000,
    calculationMethod: "percentage",
    status: "active",
  },
  {
    id: "3",
    channel: "ShopeeFood",
    commissionRate: 15,
    adsRate: 2.0,
    minimumFee: 500,
    maximumFee: 30000,
    calculationMethod: "percentage",
    status: "active",
  },
]

export default function TaxFeeRules() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Tax & Fee Rules</h1>
              <p className="text-muted-foreground">Konfigurasi aturan PPN, komisi, dan ads per channel</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Rule
              </Button>
              <Button size="sm">
                <Save className="w-4 h-4 mr-2" />
                Save All Changes
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Rules</CardTitle>
                <Calculator className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">active rules</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Fee Rules</CardTitle>
                <Percent className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">channel rules</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Commission</CardTitle>
                <Percent className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">18.3%</div>
                <p className="text-xs text-muted-foreground">across channels</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Rate</CardTitle>
                <Calculator className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">11%</div>
                <p className="text-xs text-muted-foreground">standard PPN</p>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Alert>
            <Calculator className="h-4 w-4" />
            <AlertDescription>
              Tax & Fee Rules menentukan perhitungan pajak dan fee otomatis. Aturan ini akan diterapkan saat proses
              transform dan posting ke Accurate.
            </AlertDescription>
          </Alert>

          {/* Rules Tabs */}
          <Tabs defaultValue="tax" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="tax">Tax Rules</TabsTrigger>
              <TabsTrigger value="fee">Fee Rules</TabsTrigger>
            </TabsList>

            <TabsContent value="tax" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Tax Rules Configuration</CardTitle>
                  <CardDescription>Aturan perhitungan pajak untuk pembelian dan penjualan</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {taxRules.map((rule) => (
                      <div key={rule.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="font-medium">{rule.name}</h4>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={rule.type === "purchase" ? "secondary" : "default"}>{rule.type}</Badge>
                            <Badge variant="outline">{rule.rate}%</Badge>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-destructive">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-3">
                          <div className="space-y-2">
                            <Label>Tax Rate (%)</Label>
                            <Input type="number" value={rule.rate} className="w-full" />
                          </div>
                          <div className="space-y-2">
                            <Label>Rule Type</Label>
                            <Select value={rule.type}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="purchase">Purchase</SelectItem>
                                <SelectItem value="sales">Sales</SelectItem>
                                <SelectItem value="both">Both</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Status</Label>
                            <Select value={rule.status}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="mt-4">
                          <Label>Applicable Items</Label>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {rule.applicableItems.map((item, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {item}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="fee" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Channel Fee Rules</CardTitle>
                  <CardDescription>Aturan perhitungan komisi dan ads fee per channel delivery</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {feeRules.map((rule) => (
                      <div key={rule.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="font-medium text-lg">{rule.channel}</h4>
                            <p className="text-sm text-muted-foreground">
                              Commission: {rule.commissionRate}% | Ads: {rule.adsRate}%
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={rule.status === "active" ? "default" : "secondary"}>{rule.status}</Badge>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-destructive">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-4">
                          <div className="space-y-2">
                            <Label>Commission Rate (%)</Label>
                            <Input type="number" value={rule.commissionRate} step="0.1" />
                          </div>
                          <div className="space-y-2">
                            <Label>Ads Rate (%)</Label>
                            <Input type="number" value={rule.adsRate} step="0.1" />
                          </div>
                          <div className="space-y-2">
                            <Label>Minimum Fee (Rp)</Label>
                            <Input type="number" value={rule.minimumFee} />
                          </div>
                          <div className="space-y-2">
                            <Label>Maximum Fee (Rp)</Label>
                            <Input type="number" value={rule.maximumFee} />
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2 mt-4">
                          <div className="space-y-2">
                            <Label>Calculation Method</Label>
                            <Select value={rule.calculationMethod}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="percentage">Percentage</SelectItem>
                                <SelectItem value="fixed">Fixed Amount</SelectItem>
                                <SelectItem value="tiered">Tiered</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Status</Label>
                            <Select value={rule.status}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Fee Calculator */}
          <Card>
            <CardHeader>
              <CardTitle>Fee Calculator</CardTitle>
              <CardDescription>Test perhitungan fee berdasarkan aturan yang sudah dikonfigurasi</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="space-y-2">
                  <Label>Channel</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select channel" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gofood">GoFood</SelectItem>
                      <SelectItem value="grabfood">GrabFood</SelectItem>
                      <SelectItem value="shopeefood">ShopeeFood</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Gross Sales (Rp)</Label>
                  <Input type="number" placeholder="1000000" />
                </div>
                <div className="space-y-2">
                  <Label>Calculate</Label>
                  <Button className="w-full">
                    <Calculator className="w-4 h-4 mr-2" />
                    Calculate Fees
                  </Button>
                </div>
                <div className="space-y-2">
                  <Label>Result</Label>
                  <div className="p-2 border rounded bg-muted/50 text-sm">
                    <div>Commission: Rp 0</div>
                    <div>Ads Fee: Rp 0</div>
                    <div>Net Sales: Rp 0</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
