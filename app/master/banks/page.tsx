"use client"

import { Sidebar } from "@/components/sidebar"
import { MappingTable } from "@/components/mapping-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Banknote, Upload, Download, Link } from "lucide-react"

const bankMappingData = [
  {
    id: "1",
    source: "GOFOOD-MERCHANT-12345",
    target: "GoFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-15 10:30",
    updatedBy: "FAT PSM",
  },
  {
    id: "2",
    source: "GRAB-FOOD-67890",
    target: "GrabFood",
    status: "active" as const,
    confidence: 100,
    lastUpdated: "2024-03-15 10:25",
    updatedBy: "FAT PSM",
  },
  {
    id: "3",
    source: "SHOPEE-PAY-54321",
    target: "ShopeeFood",
    status: "active" as const,
    confidence: 95,
    lastUpdated: "2024-03-15 09:15",
    updatedBy: "FAT PSM",
  },
  {
    id: "4",
    source: "TRANSFER-MANUAL",
    target: "Offline",
    status: "pending" as const,
    lastUpdated: "2024-03-14 15:20",
    updatedBy: "System",
  },
]

const channelOptions = [
  { value: "gofood", label: "GoFood" },
  { value: "grabfood", label: "GrabFood" },
  { value: "shopeefood", label: "ShopeeFood" },
  { value: "offline", label: "Offline" },
  { value: "traveloka", label: "Traveloka Eats" },
  { value: "maxim", label: "Maxim Food" },
  { value: "unknown", label: "Unknown/Other" },
]

const bankAccounts = [
  {
    id: "1",
    bank: "BCA",
    accountNumber: "**********",
    accountName: "PT Tomoro Coffee BEI",
    branch: "BEI Jakarta",
    primaryChannel: "GoFood, GrabFood",
    status: "active",
  },
  {
    id: "2",
    bank: "Mandiri",
    accountNumber: "**********",
    accountName: "PT Tomoro Coffee Lotte",
    branch: "Lotte Jakarta",
    primaryChannel: "ShopeeFood, Offline",
    status: "active",
  },
  {
    id: "3",
    bank: "BNI",
    accountNumber: "**********",
    accountName: "PT Tomoro Coffee Central",
    branch: "Central Jakarta",
    primaryChannel: "All Channels",
    status: "inactive",
  },
]

export default function BankMapping() {
  const handleAdd = (item: any) => {
    console.log("Add bank mapping:", item)
  }

  const handleEdit = (id: string, item: any) => {
    console.log("Edit bank mapping:", id, item)
  }

  const handleDelete = (id: string) => {
    console.log("Delete bank mapping:", id)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Bank Mapping</h1>
              <p className="text-muted-foreground">Mapping deskripsi mutasi rekening koran ke channel/merchant ID</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Import Patterns
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Mapping
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Bank Accounts</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">registered accounts</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Patterns</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4</div>
                <p className="text-xs text-muted-foreground">mapping patterns</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Match Rate</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">96.8%</div>
                <p className="text-xs text-muted-foreground">auto matching</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">needs attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Alert>
            <Banknote className="h-4 w-4" />
            <AlertDescription>
              Bank mapping menggunakan pattern matching untuk mengidentifikasi channel dari deskripsi transaksi bank.
              Pattern yang akurat akan meningkatkan tingkat auto-matching.
            </AlertDescription>
          </Alert>

          {/* Bank Accounts */}
          <Card>
            <CardHeader>
              <CardTitle>Registered Bank Accounts</CardTitle>
              <CardDescription>Daftar rekening bank yang terdaftar dalam sistem</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bankAccounts.map((account) => (
                  <div key={account.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <Banknote className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">
                          {account.bank} - {account.accountNumber}
                        </p>
                        <p className="text-sm text-muted-foreground">{account.accountName}</p>
                        <p className="text-xs text-muted-foreground">{account.branch}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{account.primaryChannel}</p>
                      <Badge variant={account.status === "active" ? "default" : "secondary"}>{account.status}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Bank Description Mapping */}
          <MappingTable
            title="Bank Description to Channel Mapping"
            data={bankMappingData}
            sourceLabel="Bank Description Pattern"
            targetLabel="Channel"
            targetOptions={channelOptions}
            onAdd={handleAdd}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />

          {/* Pattern Examples */}
          <Card>
            <CardHeader>
              <CardTitle>Pattern Matching Examples</CardTitle>
              <CardDescription>
                Contoh pattern yang digunakan untuk mengenali channel dari deskripsi bank
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-3">
                    <h4 className="font-medium">GoFood Patterns</h4>
                    <div className="space-y-2 text-sm">
                      <div className="p-2 bg-muted/50 rounded font-mono">GOFOOD-MERCHANT-*</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">GOJEK-FOOD-*</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">*GOFOOD*</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">GrabFood Patterns</h4>
                    <div className="space-y-2 text-sm">
                      <div className="p-2 bg-muted/50 rounded font-mono">GRAB-FOOD-*</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">GRABFOOD-*</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">*GRAB*FOOD*</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">ShopeeFood Patterns</h4>
                    <div className="space-y-2 text-sm">
                      <div className="p-2 bg-muted/50 rounded font-mono">SHOPEE-PAY-*</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">SHOPEEFOOD-*</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">*SHOPEE*FOOD*</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Unknown Patterns</h4>
                    <div className="space-y-2 text-sm">
                      <div className="p-2 bg-muted/50 rounded font-mono">TRANSFER-MANUAL</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">SETORAN-TUNAI</div>
                      <div className="p-2 bg-muted/50 rounded font-mono">*UNKNOWN*</div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <p className="text-sm text-muted-foreground">
                    Pattern menggunakan wildcard (*) untuk matching fleksibel. Case insensitive.
                  </p>
                  <Button variant="outline" size="sm">
                    <Link className="w-4 h-4 mr-2" />
                    Test Pattern
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pattern Testing */}
          <Card>
            <CardHeader>
              <CardTitle>Pattern Testing Tool</CardTitle>
              <CardDescription>Test pattern matching dengan deskripsi bank sample</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="test-description">Bank Description</Label>
                    <Input id="test-description" placeholder="e.g., GOFOOD-MERCHANT-12345" className="font-mono" />
                  </div>
                  <div className="space-y-2">
                    <Label>Matched Channel</Label>
                    <div className="p-2 border rounded bg-muted/50">
                      <span className="text-muted-foreground">Enter description to test...</span>
                    </div>
                  </div>
                </div>
                <Button>Test Pattern Match</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
