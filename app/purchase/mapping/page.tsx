import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, ArrowRight, Brain, CheckCircle, AlertTriangle, Save } from "lucide-react"

const mappingData = [
  {
    id: 1,
    bohSupplier: "PT Kopi Nusantara",
    accurateSupplier: "PT Kopi Nusantara",
    confidence: 100,
    status: "matched",
  },
  {
    id: 2,
    bohSupplier: "CV Susu Segar",
    accurateSupplier: "",
    confidence: 0,
    status: "unmapped",
  },
  {
    id: 3,
    bohSupplier: "PT Gula Manis",
    accurateSupplier: "PT Gula Manis Sejahtera",
    confidence: 85,
    status: "suggested",
  },
]

const coaMapping = [
  {
    id: 1,
    bohItem: "Arabica Beans Premium",
    suggestedCOA: "5-1100",
    coaName: "Persediaan Bahan Baku",
    confidence: 95,
    status: "auto",
  },
  {
    id: 2,
    bohItem: "Fresh Milk 1L",
    suggestedCOA: "5-1100",
    coaName: "Persediaan Bahan Baku",
    confidence: 90,
    status: "auto",
  },
  {
    id: 3,
    bohItem: "Brown Sugar",
    suggestedCOA: "",
    coaName: "",
    confidence: 0,
    status: "manual",
  },
]

const warehouseMapping = [
  {
    id: 1,
    bohWarehouse: "BEI Store",
    accurateWarehouse: "BEI-MAIN",
    confidence: 100,
    status: "matched",
  },
  {
    id: 2,
    bohWarehouse: "Lotte Store",
    accurateWarehouse: "LOTTE-MAIN",
    confidence: 100,
    status: "matched",
  },
]

export default function MappingValidation() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Mapping & Validasi</h1>
              <p className="text-muted-foreground">AI-powered mapping COA, supplier, pajak, dan gudang</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button>
                <ArrowRight className="w-4 h-4 mr-2" />
                Lanjut ke Penerimaan
              </Button>
            </div>
          </div>

          {/* AI Mapping Status */}
          <Alert>
            <Brain className="h-4 w-4" />
            <AlertDescription>
              AI telah menganalisis data dan memberikan saran mapping. Review dan approve mapping yang disarankan
              sebelum melanjutkan.
            </AlertDescription>
          </Alert>

          {/* Mapping Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Auto Mapped</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">7</div>
                <p className="text-xs text-muted-foreground">dari 10 items</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Need Review</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">manual mapping</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Confidence</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87%</div>
                <p className="text-xs text-muted-foreground">avg AI confidence</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Validation</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Ready</div>
                <p className="text-xs text-muted-foreground">untuk posting</p>
              </CardContent>
            </Card>
          </div>

          {/* Mapping Tabs */}
          <Tabs defaultValue="supplier" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="supplier">Supplier Mapping</TabsTrigger>
              <TabsTrigger value="coa">COA Mapping</TabsTrigger>
              <TabsTrigger value="warehouse">Warehouse Mapping</TabsTrigger>
              <TabsTrigger value="validation">Final Validation</TabsTrigger>
            </TabsList>

            <TabsContent value="supplier" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Supplier Mapping</CardTitle>
                  <CardDescription>Mapping supplier dari BOH ke master supplier Accurate</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mappingData.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center space-x-2">
                            <Label className="text-sm font-medium">BOH:</Label>
                            <span className="text-sm">{item.bohSupplier}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Label className="text-sm font-medium">Accurate:</Label>
                            <Select defaultValue={item.accurateSupplier}>
                              <SelectTrigger className="w-[300px]">
                                <SelectValue placeholder="Pilih supplier..." />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="PT Kopi Nusantara">PT Kopi Nusantara</SelectItem>
                                <SelectItem value="PT Gula Manis Sejahtera">PT Gula Manis Sejahtera</SelectItem>
                                <SelectItem value="CV Susu Segar Murni">CV Susu Segar Murni</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {item.confidence > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {item.confidence}% confidence
                            </Badge>
                          )}
                          <Badge
                            variant={
                              item.status === "matched"
                                ? "default"
                                : item.status === "suggested"
                                  ? "secondary"
                                  : "destructive"
                            }
                          >
                            {item.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="coa" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>COA Mapping</CardTitle>
                  <CardDescription>AI-powered mapping item ke Chart of Account</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {coaMapping.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center space-x-2">
                            <Label className="text-sm font-medium">Item:</Label>
                            <span className="text-sm">{item.bohItem}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Label className="text-sm font-medium">COA:</Label>
                            <Select defaultValue={item.suggestedCOA}>
                              <SelectTrigger className="w-[300px]">
                                <SelectValue placeholder="Pilih COA..." />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="5-1100">5-1100 - Persediaan Bahan Baku</SelectItem>
                                <SelectItem value="5-1200">5-1200 - Persediaan Barang Jadi</SelectItem>
                                <SelectItem value="5-1300">5-1300 - Persediaan Supplies</SelectItem>
                                <SelectItem value="6-1100">6-1100 - Beban Operasional</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {item.confidence > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {item.confidence}% confidence
                            </Badge>
                          )}
                          <Badge variant={item.status === "auto" ? "default" : "destructive"}>{item.status}</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="warehouse" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Warehouse Mapping</CardTitle>
                  <CardDescription>Mapping gudang BOH ke master gudang Accurate</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {warehouseMapping.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center space-x-2">
                            <Label className="text-sm font-medium">BOH:</Label>
                            <span className="text-sm">{item.bohWarehouse}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Label className="text-sm font-medium">Accurate:</Label>
                            <Select defaultValue={item.accurateWarehouse}>
                              <SelectTrigger className="w-[300px]">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="BEI-MAIN">BEI-MAIN</SelectItem>
                                <SelectItem value="LOTTE-MAIN">LOTTE-MAIN</SelectItem>
                                <SelectItem value="CENTRAL-WH">CENTRAL-WH</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary" className="text-xs">
                            {item.confidence}% confidence
                          </Badge>
                          <Badge variant="default">{item.status}</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="validation" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Final Validation</CardTitle>
                  <CardDescription>Review final mapping sebelum posting ke Accurate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Semua mapping telah selesai. Data siap untuk diposting ke Accurate.
                    </AlertDescription>
                  </Alert>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Validation Summary</Label>
                      <div className="p-3 border rounded-lg space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Supplier Mapped:</span>
                          <span className="font-medium">3/3</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>COA Mapped:</span>
                          <span className="font-medium">3/3</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Warehouse Mapped:</span>
                          <span className="font-medium">2/2</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Tax Rules Applied:</span>
                          <span className="font-medium">✓</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Posting Options</Label>
                      <div className="p-3 border rounded-lg space-y-3">
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="auto-post" defaultChecked />
                          <Label htmlFor="auto-post" className="text-sm">
                            Auto posting ke Accurate
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="send-notification" defaultChecked />
                          <Label htmlFor="send-notification" className="text-sm">
                            Kirim notifikasi setelah posting
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="create-audit" defaultChecked />
                          <Label htmlFor="create-audit" className="text-sm">
                            Buat audit trail
                          </Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button>
                      <Save className="w-4 h-4 mr-2" />
                      Save Mapping
                    </Button>
                    <Button variant="outline">Preview Accurate Format</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
