"use client"

import { Sidebar } from "@/components/sidebar"
import { DataTable } from "@/components/data-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, ArrowRight, Download, RefreshCw, CheckCircle, AlertTriangle } from "lucide-react"

const bohData = [
  {
    id: "PO-2024-001",
    tanggal: "2024-03-15",
    supplier: "PT Kopi Nusantara",
    gudang: "BEI Store",
    item: "Arabica Beans Premium",
    qty: 50,
    unit: "KG",
    harga: 85000,
    total: 4250000,
    status: "parsed",
  },
  {
    id: "PO-2024-002",
    tanggal: "2024-03-15",
    supplier: "CV Susu Segar",
    gudang: "BEI Store",
    item: "Fresh Milk 1L",
    qty: 100,
    unit: "PCS",
    harga: 15000,
    total: 1500000,
    status: "parsed",
  },
  {
    id: "PO-2024-003",
    tanggal: "2024-03-15",
    supplier: "PT Gula Manis",
    gudang: "Lotte Store",
    item: "Brown Sugar",
    qty: 25,
    unit: "KG",
    harga: 12000,
    total: 300000,
    status: "error",
  },
]

const accurateTemplate = [
  {
    dokumen: "PO-2024-001",
    tanggal: "15/03/2024",
    supplier: "PT Kopi Nusantara",
    coa: "5-1100 (Persediaan Bahan Baku)",
    item: "Arabica Beans Premium",
    qty: 50,
    harga: 85000,
    total: 4250000,
    pajak: "PPN 11%",
    gudang: "BEI-MAIN",
  },
  {
    dokumen: "PO-2024-002",
    tanggal: "15/03/2024",
    supplier: "CV Susu Segar",
    coa: "5-1100 (Persediaan Bahan Baku)",
    item: "Fresh Milk 1L",
    qty: 100,
    harga: 15000,
    total: 1500000,
    pajak: "PPN 11%",
    gudang: "BEI-MAIN",
  },
]

const bohColumns = [
  { key: "id", label: "Dokumen", sortable: true },
  { key: "tanggal", label: "Tanggal", sortable: true },
  { key: "supplier", label: "Supplier" },
  { key: "item", label: "Item" },
  { key: "qty", label: "Qty", sortable: true },
  { key: "harga", label: "Harga", render: (value: number) => `Rp ${value.toLocaleString("id-ID")}` },
  { key: "total", label: "Total", render: (value: number) => `Rp ${value.toLocaleString("id-ID")}` },
  {
    key: "status",
    label: "Status",
    render: (value: string) => <Badge variant={value === "parsed" ? "default" : "destructive"}>{value}</Badge>,
  },
]

const accurateColumns = [
  { key: "dokumen", label: "Dokumen" },
  { key: "tanggal", label: "Tanggal" },
  { key: "supplier", label: "Supplier" },
  { key: "coa", label: "COA" },
  { key: "item", label: "Item" },
  { key: "qty", label: "Qty" },
  { key: "harga", label: "Harga", render: (value: number) => `Rp ${value.toLocaleString("id-ID")}` },
  { key: "total", label: "Total", render: (value: number) => `Rp ${value.toLocaleString("id-ID")}` },
  { key: "pajak", label: "Pajak" },
  { key: "gudang", label: "Gudang" },
]

export default function TransformPreview() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Transform & Preview</h1>
              <p className="text-muted-foreground">Konversi data BOH ke template Accurate dan preview hasil</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button>
                <ArrowRight className="w-4 h-4 mr-2" />
                Lanjut ke Mapping
              </Button>
            </div>
          </div>

          {/* Transformation Status */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Records</CardTitle>
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">dari file BOH</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Successfully Parsed</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2</div>
                <p className="text-xs text-muted-foreground">siap transform</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Errors</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">perlu review</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Rp 6.05M</div>
                <p className="text-xs text-muted-foreground">nilai pembelian</p>
              </CardContent>
            </Card>
          </div>

          {/* Transformation Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Transformation Settings</CardTitle>
              <CardDescription>Konfigurasi konversi data BOH ke format Accurate</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default COA</label>
                  <Select defaultValue="5-1100">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5-1100">5-1100 (Persediaan Bahan Baku)</SelectItem>
                      <SelectItem value="5-1200">5-1200 (Persediaan Barang Jadi)</SelectItem>
                      <SelectItem value="5-1300">5-1300 (Persediaan Supplies)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Tax</label>
                  <Select defaultValue="ppn11">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ppn11">PPN 11%</SelectItem>
                      <SelectItem value="ppn0">PPN 0%</SelectItem>
                      <SelectItem value="nontax">Non Taxable</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Warehouse Mapping</label>
                  <Select defaultValue="auto">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Auto Mapping</SelectItem>
                      <SelectItem value="manual">Manual Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Button>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Transform Data
                </Button>
                <Button variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Download Template
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Data Preview Tabs */}
          <Tabs defaultValue="boh" className="space-y-4">
            <TabsList>
              <TabsTrigger value="boh">Data BOH (Original)</TabsTrigger>
              <TabsTrigger value="accurate">Template Accurate (Transformed)</TabsTrigger>
            </TabsList>

            <TabsContent value="boh">
              <DataTable
                title="Data BOH Original"
                columns={bohColumns}
                data={bohData}
                onExport={() => console.log("Export BOH data")}
              />
            </TabsContent>

            <TabsContent value="accurate">
              <Alert className="mb-4">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Data berhasil ditransform ke format Accurate. Review mapping COA, supplier, dan gudang sebelum
                  melanjutkan.
                </AlertDescription>
              </Alert>

              <DataTable
                title="Template Accurate (Preview)"
                columns={accurateColumns}
                data={accurateTemplate}
                onExport={() => console.log("Export Accurate template")}
              />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
