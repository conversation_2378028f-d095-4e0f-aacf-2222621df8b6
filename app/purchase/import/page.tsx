"use client"

import { Sidebar } from "@/components/sidebar"
import { FileUpload } from "@/components/file-upload"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { FileText, AlertCircle, CheckCircle, Clock, ArrowRight } from "lucide-react"

const recentImports = [
  {
    id: 1,
    filename: "BOH_Export_20240315.pdf",
    uploadTime: "2024-03-15 09:30:00",
    status: "completed",
    records: 45,
    errors: 0,
  },
  {
    id: 2,
    filename: "BOH_Export_20240314.csv",
    uploadTime: "2024-03-14 16:45:00",
    status: "completed",
    records: 38,
    errors: 2,
  },
  {
    id: 3,
    filename: "BOH_Export_20240313.pdf",
    uploadTime: "2024-03-13 11:20:00",
    status: "processing",
    records: 52,
    errors: 0,
  },
]

export default function ImportBOH() {
  const handleFileUpload = (files: File[]) => {
    console.log("Files uploaded:", files)
    // Here you would typically send files to your API
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Import BOH</h1>
              <p className="text-muted-foreground">Upload dan parsing file BOH (PDF/CSV) untuk proses pembelian</p>
            </div>
            <Button>
              <ArrowRight className="w-4 h-4 mr-2" />
              Lanjut ke Transform
            </Button>
          </div>

          {/* Instructions */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Upload file BOH dalam format PDF atau CSV. Sistem akan otomatis melakukan parsing dan validasi data.
              Pastikan file mengandung informasi: dokumen, tanggal, supplier, gudang, item, qty, dan harga.
            </AlertDescription>
          </Alert>

          {/* File Upload */}
          <FileUpload onFileUpload={handleFileUpload} />

          {/* Processing Status */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Files Processed</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">127</div>
                <p className="text-xs text-muted-foreground">+12 from yesterday</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">96.8%</div>
                <Progress value={96.8} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.3s</div>
                <p className="text-xs text-muted-foreground">per file</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Imports */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Imports</CardTitle>
              <CardDescription>File BOH yang baru saja diproses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentImports.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <FileText className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{item.filename}</p>
                        <p className="text-sm text-muted-foreground">{item.uploadTime}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">{item.records} records</p>
                        {item.errors > 0 && <p className="text-sm text-destructive">{item.errors} errors</p>}
                      </div>
                      <Badge
                        variant={
                          item.status === "completed"
                            ? "default"
                            : item.status === "processing"
                              ? "secondary"
                              : "destructive"
                        }
                      >
                        {item.status}
                      </Badge>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
