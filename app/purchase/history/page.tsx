"use client"

import { Sidebar } from "@/components/sidebar"
import { DataTable } from "@/components/data-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ArrowLeft, CalendarIcon, Eye, RotateCcw } from "lucide-react"
import { format } from "date-fns"
import { id } from "date-fns/locale"
import { useState } from "react"

const historyData = [
  {
    id: "BATCH-2024-001",
    tanggal: "2024-03-15 10:30:00",
    user: "FAT PSM",
    action: "Import & Transform",
    documents: 3,
    status: "success",
    accurateStatus: "posted",
    totalValue: 6050000,
    notes: "Auto posting berhasil",
  },
  {
    id: "BATCH-2024-002",
    tanggal: "2024-03-14 15:45:00",
    user: "FAT PSM",
    action: "Manual Mapping",
    documents: 5,
    status: "success",
    accurateStatus: "posted",
    totalValue: 8750000,
    notes: "Manual review COA mapping",
  },
  {
    id: "BATCH-2024-003",
    tanggal: "2024-03-13 09:15:00",
    user: "FAT PSM",
    action: "Import & Transform",
    documents: 2,
    status: "error",
    accurateStatus: "failed",
    totalValue: 2300000,
    notes: "Supplier mapping error",
  },
  {
    id: "BATCH-2024-004",
    tanggal: "2024-03-12 14:20:00",
    user: "OPS BEI",
    action: "Receiving Update",
    documents: 4,
    status: "success",
    accurateStatus: "posted",
    totalValue: 5200000,
    notes: "Partial receiving processed",
  },
]

const auditData = [
  {
    id: 1,
    timestamp: "2024-03-15 10:32:15",
    user: "FAT PSM",
    action: "UPDATE",
    table: "purchase_orders",
    recordId: "PO-2024-001",
    field: "supplier_id",
    oldValue: "SUP-001",
    newValue: "SUP-002",
    reason: "Supplier mapping correction",
  },
  {
    id: 2,
    timestamp: "2024-03-15 10:31:45",
    user: "System",
    action: "INSERT",
    table: "accurate_postings",
    recordId: "POST-2024-001",
    field: "status",
    oldValue: null,
    newValue: "success",
    reason: "Auto posting completed",
  },
  {
    id: 3,
    timestamp: "2024-03-15 10:30:30",
    user: "FAT PSM",
    action: "UPDATE",
    table: "coa_mappings",
    recordId: "MAP-001",
    field: "coa_code",
    oldValue: "5-1200",
    newValue: "5-1100",
    reason: "COA classification update",
  },
]

const historyColumns = [
  { key: "id", label: "Batch ID", sortable: true },
  { key: "tanggal", label: "Tanggal", sortable: true },
  { key: "user", label: "User" },
  { key: "action", label: "Action" },
  { key: "documents", label: "Docs", sortable: true },
  {
    key: "status",
    label: "Status",
    render: (value: string) => <Badge variant={value === "success" ? "default" : "destructive"}>{value}</Badge>,
  },
  {
    key: "accurateStatus",
    label: "Accurate",
    render: (value: string) => (
      <Badge variant={value === "posted" ? "default" : value === "failed" ? "destructive" : "secondary"}>{value}</Badge>
    ),
  },
  {
    key: "totalValue",
    label: "Total Value",
    render: (value: number) => `Rp ${value.toLocaleString("id-ID")}`,
  },
]

const auditColumns = [
  { key: "timestamp", label: "Timestamp", sortable: true },
  { key: "user", label: "User" },
  { key: "action", label: "Action" },
  { key: "table", label: "Table" },
  { key: "recordId", label: "Record ID" },
  { key: "field", label: "Field" },
  { key: "oldValue", label: "Old Value" },
  { key: "newValue", label: "New Value" },
  { key: "reason", label: "Reason" },
]

export default function PurchaseHistory() {
  const [dateFrom, setDateFrom] = useState<Date>()
  const [dateTo, setDateTo] = useState<Date>()

  const handleView = (row: any) => {
    console.log("View batch details:", row)
  }

  const handleRetry = (row: any) => {
    console.log("Retry batch:", row)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">History & Audit</h1>
              <p className="text-muted-foreground">Log setiap batch, perubahan, user, dan audit trail lengkap</p>
            </div>
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali ke Dashboard
            </Button>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
              <CardDescription>Filter data history berdasarkan tanggal, user, atau status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-5">
                <div className="space-y-2">
                  <Label>From Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal bg-transparent">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateFrom ? format(dateFrom, "PPP", { locale: id }) : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar mode="single" selected={dateFrom} onSelect={setDateFrom} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>To Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal bg-transparent">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateTo ? format(dateTo, "PPP", { locale: id }) : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar mode="single" selected={dateTo} onSelect={setDateTo} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>User</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All Users" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="fat-psm">FAT PSM</SelectItem>
                      <SelectItem value="ops-bei">OPS BEI</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Action</Label>
                  <Button className="w-full">Apply Filters</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* History Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Batches</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">127</div>
                <p className="text-xs text-muted-foreground">all time</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94.5%</div>
                <p className="text-xs text-muted-foreground">last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed Batches</CardTitle>
                <RotateCcw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">7</div>
                <p className="text-xs text-muted-foreground">need retry</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Rp 2.1B</div>
                <p className="text-xs text-muted-foreground">processed</p>
              </CardContent>
            </Card>
          </div>

          {/* Batch History */}
          <DataTable
            title="Batch History"
            columns={historyColumns}
            data={historyData}
            onEdit={handleView}
            onExport={() => console.log("Export history")}
          />

          {/* Audit Trail */}
          <DataTable
            title="Audit Trail"
            columns={auditColumns}
            data={auditData}
            actions={false}
            onExport={() => console.log("Export audit trail")}
          />
        </div>
      </main>
    </div>
  )
}
