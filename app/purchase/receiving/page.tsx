"use client"

import { Sidebar } from "@/components/sidebar"
import { DataTable } from "@/components/data-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ArrowLeft, ArrowRight, Package, CheckCircle, AlertTriangle, Eye, Edit } from "lucide-react"

const receivingData = [
  {
    id: "GR-2024-001",
    poNumber: "PO-2024-001",
    tanggal: "2024-03-15",
    supplier: "PT Kopi Nusantara",
    item: "Arabica Beans Premium",
    qtyOrdered: 50,
    qtyReceived: 50,
    qtyVariance: 0,
    status: "completed",
    doNumber: "DO-KB-001",
    invoiceNumber: "INV-KB-001",
  },
  {
    id: "GR-2024-002",
    poNumber: "PO-2024-002",
    tanggal: "2024-03-15",
    supplier: "CV Susu Segar",
    item: "Fresh Milk 1L",
    qtyOrdered: 100,
    qtyReceived: 95,
    qtyVariance: -5,
    status: "partial",
    doNumber: "DO-SS-002",
    invoiceNumber: "INV-SS-002",
  },
  {
    id: "GR-2024-003",
    poNumber: "PO-2024-003",
    tanggal: "2024-03-15",
    supplier: "PT Gula Manis",
    item: "Brown Sugar",
    qtyOrdered: 25,
    qtyReceived: 0,
    qtyVariance: -25,
    status: "pending",
    doNumber: "",
    invoiceNumber: "",
  },
]

const receivingColumns = [
  { key: "id", label: "GR Number", sortable: true },
  { key: "poNumber", label: "PO Number", sortable: true },
  { key: "tanggal", label: "Tanggal", sortable: true },
  { key: "supplier", label: "Supplier" },
  { key: "item", label: "Item" },
  { key: "qtyOrdered", label: "Qty Ordered", sortable: true },
  { key: "qtyReceived", label: "Qty Received", sortable: true },
  {
    key: "qtyVariance",
    label: "Variance",
    render: (value: number) => (
      <span className={value < 0 ? "text-destructive" : value > 0 ? "text-green-600" : ""}>
        {value > 0 ? "+" : ""}
        {value}
      </span>
    ),
  },
  {
    key: "status",
    label: "Status",
    render: (value: string) => (
      <Badge variant={value === "completed" ? "default" : value === "partial" ? "secondary" : "destructive"}>
        {value}
      </Badge>
    ),
  },
]

export default function Receiving() {
  const handleEdit = (row: any) => {
    console.log("Edit receiving:", row)
  }

  const handleView = (row: any) => {
    console.log("View receiving details:", row)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Penerimaan Barang</h1>
              <p className="text-muted-foreground">Match DO/Invoice dengan PO, validasi kuantitas dan harga</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button>
                <ArrowRight className="w-4 h-4 mr-2" />
                Lanjut ke History
              </Button>
            </div>
          </div>

          {/* Receiving Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Receiving</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">dokumen hari ini</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">selesai diterima</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Partial</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">penerimaan sebagian</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">menunggu barang</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Aksi cepat untuk proses penerimaan</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="h-20 flex-col space-y-2">
                      <Package className="h-6 w-6" />
                      <span className="text-sm">Record Receiving</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Record New Receiving</DialogTitle>
                      <DialogDescription>Catat penerimaan barang baru dengan matching DO dan Invoice</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="po-number">PO Number</Label>
                          <Input id="po-number" placeholder="PO-2024-XXX" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="do-number">DO Number</Label>
                          <Input id="do-number" placeholder="DO-XXX-XXX" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="invoice-number">Invoice Number</Label>
                          <Input id="invoice-number" placeholder="INV-XXX-XXX" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="received-qty">Qty Received</Label>
                          <Input id="received-qty" type="number" placeholder="0" />
                        </div>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline">Cancel</Button>
                        <Button>Save Receiving</Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button variant="outline" className="h-20 flex-col space-y-2 bg-transparent">
                  <CheckCircle className="h-6 w-6" />
                  <span className="text-sm">Batch Approve</span>
                </Button>

                <Button variant="outline" className="h-20 flex-col space-y-2 bg-transparent">
                  <AlertTriangle className="h-6 w-6" />
                  <span className="text-sm">Review Variances</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Receiving Data Table */}
          <DataTable
            title="Receiving Records"
            columns={receivingColumns}
            data={receivingData}
            onEdit={handleEdit}
            onExport={() => console.log("Export receiving data")}
          />

          {/* Variance Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Variance Analysis</CardTitle>
              <CardDescription>Analisis selisih quantity dan harga</CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Ditemukan 2 item dengan variance. Review dan approve sebelum posting ke Accurate.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Fresh Milk 1L - CV Susu Segar</h4>
                    <Badge variant="secondary">Partial Receiving</Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Ordered:</span>
                      <span className="ml-2 font-medium">100 PCS</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Received:</span>
                      <span className="ml-2 font-medium">95 PCS</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Variance:</span>
                      <span className="ml-2 font-medium text-destructive">-5 PCS</span>
                    </div>
                  </div>
                  <div className="mt-3 flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                    <Button size="sm">
                      <Edit className="w-4 h-4 mr-2" />
                      Adjust Quantity
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Brown Sugar - PT Gula Manis</h4>
                    <Badge variant="destructive">Not Received</Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Ordered:</span>
                      <span className="ml-2 font-medium">25 KG</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Received:</span>
                      <span className="ml-2 font-medium">0 KG</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Variance:</span>
                      <span className="ml-2 font-medium text-destructive">-25 KG</span>
                    </div>
                  </div>
                  <div className="mt-3 flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                    <Button size="sm">
                      <Package className="w-4 h-4 mr-2" />
                      Record Receiving
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
