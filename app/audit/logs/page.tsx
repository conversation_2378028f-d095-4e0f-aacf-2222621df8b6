import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileText, Download, Filter, Search, Eye, User, Clock, Activity } from "lucide-react"

const auditLogs = [
  {
    id: "1",
    timestamp: "2024-12-15 14:30:25",
    user: "<EMAIL>",
    action: "POST_TO_ACCURATE",
    module: "Sales",
    details: "Posted 25 sales invoices to Accurate",
    status: "success",
    ipAddress: "*************",
    userAgent: "Chrome/120.0.0.0",
    before: null,
    after: "25 invoices created",
  },
  {
    id: "2",
    timestamp: "2024-12-15 14:15:10",
    user: "<EMAIL>",
    action: "UPLOAD_BOH_FILE",
    module: "Purchase",
    details: "Uploaded PO_BEI_20241215.csv",
    status: "success",
    ipAddress: "*************",
    userAgent: "Chrome/120.0.0.0",
    before: null,
    after: "156 records parsed",
  },
  {
    id: "3",
    timestamp: "2024-12-15 14:00:45",
    user: "<EMAIL>",
    action: "UPDATE_COA_MAPPING",
    module: "Master Data",
    details: "Updated COA mapping for Sales - GoFood",
    status: "success",
    ipAddress: "*************",
    userAgent: "Chrome/120.0.0.0",
    before: "Account: 4001-001",
    after: "Account: 4001-002",
  },
  {
    id: "4",
    timestamp: "2024-12-15 13:45:30",
    user: "system",
    action: "AUTO_RECONCILIATION",
    module: "Sales",
    details: "Automatic reconciliation failed - bank API timeout",
    status: "error",
    ipAddress: "127.0.0.1",
    userAgent: "System Scheduler",
    before: null,
    after: "Error: Connection timeout",
  },
  {
    id: "5",
    timestamp: "2024-12-15 13:30:15",
    user: "<EMAIL>",
    action: "CREATE_USER",
    module: "User Management",
    details: "Created new user <NAME_EMAIL>",
    status: "success",
    ipAddress: "************",
    userAgent: "Chrome/120.0.0.0",
    before: null,
    after: "User created with OPS role",
  },
]

export default function AuditLogsPage() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Audit Logs</h1>
              <p className="text-muted-foreground">Comprehensive activity tracking and audit trail</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Logs
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Advanced Filter
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,847</div>
                <p className="text-xs text-muted-foreground">last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">unique users today</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <Clock className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">97.8%</div>
                <p className="text-xs text-muted-foreground">successful operations</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Critical Events</CardTitle>
                <FileText className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">require attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filter & Search</CardTitle>
              <CardDescription>Filter audit logs by various criteria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-5">
                <div className="space-y-2">
                  <Label>Search</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search logs..." className="pl-8" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>User</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All users" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="fatpsm"><EMAIL></SelectItem>
                      <SelectItem value="ops"><EMAIL></SelectItem>
                      <SelectItem value="admin"><EMAIL></SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Module</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All modules" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Modules</SelectItem>
                      <SelectItem value="purchase">Purchase</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="master">Master Data</SelectItem>
                      <SelectItem value="integration">Integration</SelectItem>
                      <SelectItem value="user">User Management</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Last 7 days" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">Last 7 days</SelectItem>
                      <SelectItem value="month">Last 30 days</SelectItem>
                      <SelectItem value="custom">Custom range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Audit Logs Table */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Log</CardTitle>
              <CardDescription>Detailed audit trail of all system activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {auditLogs.map((log) => (
                  <div key={log.id} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <Badge
                          variant={
                            log.status === "success" ? "default" : log.status === "error" ? "destructive" : "secondary"
                          }
                        >
                          {log.status}
                        </Badge>
                        <span className="font-medium">{log.action}</span>
                        <Badge variant="outline">{log.module}</Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">{log.timestamp}</span>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <p className="text-sm mb-2">{log.details}</p>

                    <div className="grid gap-2 md:grid-cols-3 text-xs text-muted-foreground">
                      <div>
                        <span className="font-medium">User:</span> {log.user}
                      </div>
                      <div>
                        <span className="font-medium">IP:</span> {log.ipAddress}
                      </div>
                      <div>
                        <span className="font-medium">Browser:</span> {log.userAgent.split("/")[0]}
                      </div>
                    </div>

                    {(log.before || log.after) && (
                      <div className="mt-3 pt-3 border-t">
                        <div className="grid gap-2 md:grid-cols-2 text-xs">
                          {log.before && (
                            <div>
                              <span className="font-medium text-red-600">Before:</span>
                              <p className="text-muted-foreground">{log.before}</p>
                            </div>
                          )}
                          {log.after && (
                            <div>
                              <span className="font-medium text-green-600">After:</span>
                              <p className="text-muted-foreground">{log.after}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-muted-foreground">Showing 1-5 of 2,847 entries</p>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" disabled>
                    Previous
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
