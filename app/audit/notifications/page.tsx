import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Bell, Mail, MessageSquare, Settings, CheckCircle, XCircle, AlertTriangle, Smartphone } from "lucide-react"

const notificationRules = [
  {
    id: "1",
    name: "Critical System Errors",
    description: "Notify when system encounters critical errors",
    trigger: "error",
    channels: ["email", "whatsapp"],
    recipients: ["<EMAIL>", "+628123456789"],
    enabled: true,
    lastTriggered: "2024-12-15 13:45:30",
  },
  {
    id: "2",
    name: "Daily Processing Summary",
    description: "Daily summary of all processing activities",
    trigger: "schedule",
    channels: ["email"],
    recipients: ["<EMAIL>", "<EMAIL>"],
    enabled: true,
    lastTriggered: "2024-12-15 08:00:00",
  },
  {
    id: "3",
    name: "Large Discrepancies",
    description: "Alert when reconciliation discrepancies exceed threshold",
    trigger: "threshold",
    channels: ["email", "slack"],
    recipients: ["<EMAIL>"],
    enabled: true,
    lastTriggered: "2024-12-14 16:30:00",
  },
  {
    id: "4",
    name: "Failed API Calls",
    description: "Notify when API calls to Accurate fail",
    trigger: "api_failure",
    channels: ["email"],
    recipients: ["<EMAIL>"],
    enabled: false,
    lastTriggered: "Never",
  },
]

const recentNotifications = [
  {
    id: "1",
    timestamp: "2024-12-15 14:30:00",
    type: "success",
    title: "Daily Processing Complete",
    message: "All scheduled jobs completed successfully. 156 transactions processed.",
    channels: ["email"],
    status: "sent",
  },
  {
    id: "2",
    timestamp: "2024-12-15 13:45:30",
    type: "error",
    title: "Bank API Connection Failed",
    message: "Unable to connect to bank API for reconciliation. Retrying in 5 minutes.",
    channels: ["email", "whatsapp"],
    status: "sent",
  },
  {
    id: "3",
    timestamp: "2024-12-15 12:00:00",
    type: "warning",
    title: "Large Discrepancy Detected",
    message: "GoFood reconciliation shows discrepancy of Rp 125,000. Please review.",
    channels: ["email"],
    status: "sent",
  },
  {
    id: "4",
    timestamp: "2024-12-15 09:00:00",
    type: "info",
    title: "BOH Data Sync Started",
    message: "Processing 89 purchase orders from BOH system.",
    channels: ["email"],
    status: "sent",
  },
]

export default function NotificationsPage() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
              <p className="text-muted-foreground">Manage alerts and notification settings</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Bell className="w-4 h-4 mr-2" />
                Test Notification
              </Button>
              <Button size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Global Settings
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Rules</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">notification rules</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Today's Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">notifications sent</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">99.2%</div>
                <p className="text-xs text-muted-foreground">successful delivery</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed Alerts</CardTitle>
                <XCircle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">needs attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Notification Management */}
          <Tabs defaultValue="rules" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="rules">Notification Rules</TabsTrigger>
              <TabsTrigger value="channels">Channels</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="rules" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Rules</CardTitle>
                  <CardDescription>Configure when and how to send notifications</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {notificationRules.map((rule) => (
                      <div key={rule.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Switch checked={rule.enabled} />
                            <div>
                              <h4 className="font-medium">{rule.name}</h4>
                              <p className="text-sm text-muted-foreground">{rule.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{rule.trigger}</Badge>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-3 text-sm">
                          <div>
                            <Label className="text-xs text-muted-foreground">Channels</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {rule.channels.map((channel, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {channel}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Recipients</Label>
                            <p className="font-medium">{rule.recipients.length} recipients</p>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Last Triggered</Label>
                            <p className="font-medium">{rule.lastTriggered}</p>
                          </div>
                        </div>

                        {/* Rule Configuration */}
                        <div className="mt-4 pt-4 border-t">
                          <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                              <Label className="text-sm">Trigger Condition</Label>
                              <Select defaultValue={rule.trigger}>
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="error">System Error</SelectItem>
                                  <SelectItem value="warning">Warning</SelectItem>
                                  <SelectItem value="threshold">Threshold Exceeded</SelectItem>
                                  <SelectItem value="schedule">Scheduled</SelectItem>
                                  <SelectItem value="api_failure">API Failure</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm">Priority</Label>
                              <Select defaultValue="normal">
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="critical">Critical</SelectItem>
                                  <SelectItem value="high">High</SelectItem>
                                  <SelectItem value="normal">Normal</SelectItem>
                                  <SelectItem value="low">Low</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="channels" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Mail className="h-5 w-5" />
                      <span>Email Configuration</span>
                    </CardTitle>
                    <CardDescription>Setup email notification settings</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>SMTP Server</Label>
                      <Input defaultValue="smtp.gmail.com" />
                    </div>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Port</Label>
                        <Input defaultValue="587" />
                      </div>
                      <div className="space-y-2">
                        <Label>Security</Label>
                        <Select defaultValue="tls">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="tls">TLS</SelectItem>
                            <SelectItem value="ssl">SSL</SelectItem>
                            <SelectItem value="none">None</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>From Email</Label>
                      <Input defaultValue="<EMAIL>" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="email-enabled" defaultChecked />
                      <Label htmlFor="email-enabled">Enable email notifications</Label>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Smartphone className="h-5 w-5" />
                      <span>WhatsApp Configuration</span>
                    </CardTitle>
                    <CardDescription>Setup WhatsApp notification settings</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>API Endpoint</Label>
                      <Input placeholder="https://api.whatsapp.com/send" />
                    </div>
                    <div className="space-y-2">
                      <Label>API Token</Label>
                      <Input type="password" placeholder="••••••••••••••••••••" />
                    </div>
                    <div className="space-y-2">
                      <Label>Phone Number</Label>
                      <Input placeholder="+628123456789" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="whatsapp-enabled" />
                      <Label htmlFor="whatsapp-enabled">Enable WhatsApp notifications</Label>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MessageSquare className="h-5 w-5" />
                      <span>Slack Configuration</span>
                    </CardTitle>
                    <CardDescription>Setup Slack notification settings</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Webhook URL</Label>
                      <Input placeholder="https://hooks.slack.com/services/..." />
                    </div>
                    <div className="space-y-2">
                      <Label>Channel</Label>
                      <Input defaultValue="#tomoro-alerts" />
                    </div>
                    <div className="space-y-2">
                      <Label>Bot Name</Label>
                      <Input defaultValue="Tomoro Bot" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="slack-enabled" />
                      <Label htmlFor="slack-enabled">Enable Slack notifications</Label>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Global Settings</CardTitle>
                    <CardDescription>General notification preferences</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Quiet Hours</Label>
                      <div className="grid gap-2 md:grid-cols-2">
                        <Input type="time" defaultValue="22:00" />
                        <Input type="time" defaultValue="06:00" />
                      </div>
                      <p className="text-xs text-muted-foreground">No notifications will be sent during quiet hours</p>
                    </div>
                    <div className="space-y-2">
                      <Label>Rate Limiting</Label>
                      <Input type="number" defaultValue="10" />
                      <p className="text-xs text-muted-foreground">Maximum notifications per hour</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="digest-mode" />
                      <Label htmlFor="digest-mode">Enable digest mode (batch notifications)</Label>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Notification History</CardTitle>
                  <CardDescription>Recent notifications sent by the system</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentNotifications.map((notification) => (
                      <div key={notification.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              {notification.type === "success" && <CheckCircle className="h-5 w-5 text-green-500" />}
                              {notification.type === "error" && <XCircle className="h-5 w-5 text-red-500" />}
                              {notification.type === "warning" && <AlertTriangle className="h-5 w-5 text-yellow-500" />}
                              {notification.type === "info" && <Bell className="h-5 w-5 text-blue-500" />}
                            </div>
                            <div>
                              <h4 className="font-medium">{notification.title}</h4>
                              <p className="text-sm text-muted-foreground">{notification.message}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={notification.status === "sent" ? "default" : "secondary"}>
                              {notification.status}
                            </Badge>
                            <span className="text-sm text-muted-foreground">{notification.timestamp}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 mt-2">
                          <Label className="text-xs text-muted-foreground">Channels:</Label>
                          {notification.channels.map((channel, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {channel}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
