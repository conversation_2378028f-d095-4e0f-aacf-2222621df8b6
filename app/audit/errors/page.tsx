"use client"

import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertTriangle, XCircle, AlertCircle, RefreshCw, Search, Filter } from "lucide-react"

export default function ErrorCenterPage() {
  const errorStats = [
    { label: "Critical Errors", count: "3", icon: XCircle, color: "text-red-500" },
    { label: "Warnings", count: "12", icon: AlertTriangle, color: "text-yellow-500" },
    { label: "Info", count: "45", icon: AlertCircle, color: "text-blue-500" },
    { label: "Resolved", count: "128", icon: RefreshCw, color: "text-green-500" },
  ]

  const recentErrors = [
    {
      id: 1,
      type: "critical",
      title: "Database Connection Failed",
      description: "Failed to connect to primary database server",
      timestamp: "2024-01-15 11:30:45",
      source: "Database",
      status: "active",
    },
    {
      id: 2,
      type: "warning",
      title: "API Rate Limit Exceeded",
      description: "Bank API rate limit exceeded for BCA integration",
      timestamp: "2024-01-15 11:25:12",
      source: "Integration",
      status: "active",
    },
    {
      id: 3,
      type: "error",
      title: "File Upload Failed",
      description: "Failed to process uploaded transaction file",
      timestamp: "2024-01-15 11:20:33",
      source: "File Processing",
      status: "resolved",
    },
    {
      id: 4,
      type: "warning",
      title: "Sync Delay Detected",
      description: "BI sync taking longer than expected",
      timestamp: "2024-01-15 11:15:22",
      source: "BI Integration",
      status: "active",
    },
  ]

  const getErrorBadge = (type: string) => {
    switch (type) {
      case "critical":
        return <Badge variant="destructive">Critical</Badge>
      case "warning":
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            Warning
          </Badge>
        )
      case "error":
        return (
          <Badge variant="outline" className="border-red-200 text-red-700">
            Error
          </Badge>
        )
      default:
        return <Badge variant="outline">Info</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    return status === "active" ? <Badge variant="destructive">Active</Badge> : <Badge variant="default">Resolved</Badge>
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Error Center</h1>
              <p className="text-muted-foreground">Monitor dan kelola error sistem secara terpusat</p>
            </div>
            <Button>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {errorStats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{stat.label}</CardTitle>
                    <Icon className={`h-4 w-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stat.count}</div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          <Tabs defaultValue="active" className="space-y-4">
            <TabsList>
              <TabsTrigger value="active">Error Aktif</TabsTrigger>
              <TabsTrigger value="resolved">Resolved</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Cari error..." className="pl-8" />
                </div>
                <Select>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Type</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {recentErrors
                  .filter((error) => error.status === "active")
                  .map((error) => (
                    <Card key={error.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getErrorBadge(error.type)}
                            {getStatusBadge(error.status)}
                          </div>
                          <div className="text-sm text-muted-foreground">{error.timestamp}</div>
                        </div>
                        <CardTitle className="text-base">{error.title}</CardTitle>
                        <CardDescription>{error.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">Source: {error.source}</div>
                          <div className="space-x-2">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                            <Button variant="outline" size="sm">
                              Mark Resolved
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>

            <TabsContent value="resolved" className="space-y-4">
              <div className="space-y-4">
                {recentErrors
                  .filter((error) => error.status === "resolved")
                  .map((error) => (
                    <Card key={error.id} className="opacity-75">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getErrorBadge(error.type)}
                            {getStatusBadge(error.status)}
                          </div>
                          <div className="text-sm text-muted-foreground">{error.timestamp}</div>
                        </div>
                        <CardTitle className="text-base">{error.title}</CardTitle>
                        <CardDescription>{error.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">Source: {error.source}</div>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Error Analytics</CardTitle>
                  <CardDescription>Analisis trend dan pattern error sistem</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    Error analytics dashboard akan ditampilkan di sini
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
