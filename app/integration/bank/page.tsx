"use client"

import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, CheckCircle, Settings, Plus, Trash2 } from "lucide-react"

export default function BankConnectionPage() {
  const bankConnections = [
    {
      id: 1,
      name: "BCA API",
      type: "Open Banking",
      status: "connected",
      lastSync: "2024-01-15 10:30",
      accounts: 3,
    },
    {
      id: 2,
      name: "Mandiri Corporate",
      type: "Corporate Banking",
      status: "connected",
      lastSync: "2024-01-15 09:45",
      accounts: 2,
    },
    {
      id: 3,
      name: "BNI Business",
      type: "Business Banking",
      status: "error",
      lastSync: "2024-01-14 16:20",
      accounts: 1,
    },
  ]

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Koneksi Bank</h1>
              <p className="text-muted-foreground">
                Kelola koneksi ke sistem perbankan untuk sinkronisasi data transaksi
              </p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Tambah Koneksi
            </Button>
          </div>

          <Tabs defaultValue="connections" className="space-y-4">
            <TabsList>
              <TabsTrigger value="connections">Koneksi Aktif</TabsTrigger>
              <TabsTrigger value="settings">Pengaturan</TabsTrigger>
            </TabsList>

            <TabsContent value="connections" className="space-y-4">
              <div className="grid gap-4">
                {bankConnections.map((connection) => (
                  <Card key={connection.id}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div className="space-y-1">
                        <CardTitle className="text-base">{connection.name}</CardTitle>
                        <CardDescription>{connection.type}</CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={connection.status === "connected" ? "default" : "destructive"}>
                          {connection.status === "connected" ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <AlertCircle className="h-3 w-3 mr-1" />
                          )}
                          {connection.status === "connected" ? "Terhubung" : "Error"}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Terakhir Sinkron</p>
                          <p className="font-medium">{connection.lastSync}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Akun Terhubung</p>
                          <p className="font-medium">{connection.accounts} akun</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pengaturan Sinkronisasi</CardTitle>
                  <CardDescription>Konfigurasi otomatis sinkronisasi data bank</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Auto Sync</Label>
                      <p className="text-sm text-muted-foreground">Sinkronisasi otomatis setiap 30 menit</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sync-interval">Interval Sinkronisasi (menit)</Label>
                    <Input id="sync-interval" type="number" defaultValue="30" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="retry-attempts">Maksimal Retry</Label>
                    <Input id="retry-attempts" type="number" defaultValue="3" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
