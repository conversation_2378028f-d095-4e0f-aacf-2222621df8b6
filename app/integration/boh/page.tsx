import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Database, TestTube, Settings, CheckCircle, XCircle, AlertTriangle } from "lucide-react"

export default function BOHIntegration() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">BOH Integration</h1>
              <p className="text-muted-foreground">Konfigurasi koneksi ke sistem Back of House Tomoro</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <TestTube className="w-4 h-4 mr-2" />
                Test Connection
              </Button>
              <Button size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Save Configuration
              </Button>
            </div>
          </div>

          {/* Connection Status */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Connected</div>
                <p className="text-xs text-muted-foreground">Last sync: 2 minutes ago</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Export Format</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">CSV/PDF</div>
                <p className="text-xs text-muted-foreground">Auto-detection enabled</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Last Import</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">156</div>
                <p className="text-xs text-muted-foreground">documents processed</p>
              </CardContent>
            </Card>
          </div>

          {/* Configuration */}
          <Tabs defaultValue="connection" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="connection">Connection</TabsTrigger>
              <TabsTrigger value="format">Export Format</TabsTrigger>
              <TabsTrigger value="schedule">Schedule</TabsTrigger>
            </TabsList>

            <TabsContent value="connection" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>BOH Connection Settings</CardTitle>
                  <CardDescription>Konfigurasi koneksi ke sistem BOH Tomoro</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Connection Type</Label>
                      <Select defaultValue="manual">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manual">Manual Upload</SelectItem>
                          <SelectItem value="sftp">SFTP</SelectItem>
                          <SelectItem value="api">API (Future)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Store Location</Label>
                      <Select defaultValue="bei">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bei">BEI</SelectItem>
                          <SelectItem value="lotte">Lotte Mall Jakarta</SelectItem>
                          <SelectItem value="both">Both Locations</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Upload Directory</Label>
                    <Input placeholder="/uploads/boh/" />
                    <p className="text-xs text-muted-foreground">Directory untuk menyimpan file BOH yang diupload</p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="auto-process" />
                    <Label htmlFor="auto-process">Auto-process uploaded files</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="backup" defaultChecked />
                    <Label htmlFor="backup">Backup original files</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="format" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Export Format Configuration</CardTitle>
                  <CardDescription>Aturan parsing untuk file BOH</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <Database className="h-4 w-4" />
                    <AlertDescription>
                      Sistem akan otomatis mendeteksi format file (CSV/PDF) dan melakukan parsing sesuai template.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-4">
                    <div>
                      <Label className="text-base font-medium">CSV Format Settings</Label>
                      <div className="grid gap-4 md:grid-cols-2 mt-2">
                        <div className="space-y-2">
                          <Label>Delimiter</Label>
                          <Select defaultValue="comma">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="comma">Comma (,)</SelectItem>
                              <SelectItem value="semicolon">Semicolon (;)</SelectItem>
                              <SelectItem value="tab">Tab</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Encoding</Label>
                          <Select defaultValue="utf8">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="utf8">UTF-8</SelectItem>
                              <SelectItem value="latin1">Latin-1</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-base font-medium">PDF Parsing Settings</Label>
                      <div className="grid gap-4 md:grid-cols-2 mt-2">
                        <div className="space-y-2">
                          <Label>Table Detection</Label>
                          <Select defaultValue="auto">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="auto">Auto-detect</SelectItem>
                              <SelectItem value="manual">Manual regions</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>OCR Quality</Label>
                          <Select defaultValue="high">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="fast">Fast</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="schedule" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Processing Schedule</CardTitle>
                  <CardDescription>Jadwal otomatis untuk memproses file BOH</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch id="auto-schedule" />
                    <Label htmlFor="auto-schedule">Enable automatic processing</Label>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Processing Time</Label>
                      <Input type="time" defaultValue="09:00" />
                    </div>
                    <div className="space-y-2">
                      <Label>Frequency</Label>
                      <Select defaultValue="daily">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">Every Hour</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Retry Policy</Label>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <Label className="text-sm">Max Retries</Label>
                        <Input type="number" defaultValue="3" />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm">Retry Interval (min)</Label>
                        <Input type="number" defaultValue="5" />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm">Timeout (min)</Label>
                        <Input type="number" defaultValue="30" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent BOH Processing Activity</CardTitle>
              <CardDescription>Log aktivitas terbaru dari sistem BOH</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    time: "10:30 AM",
                    action: "File processed: PO_BEI_20241215.csv",
                    status: "success",
                    details: "156 records imported successfully",
                  },
                  {
                    time: "09:15 AM",
                    action: "File uploaded: GR_LOTTE_20241215.pdf",
                    status: "processing",
                    details: "PDF parsing in progress...",
                  },
                  {
                    time: "08:45 AM",
                    action: "Scheduled sync completed",
                    status: "success",
                    details: "All pending files processed",
                  },
                  {
                    time: "08:30 AM",
                    action: "File validation failed: INV_BEI_20241214.csv",
                    status: "error",
                    details: "Missing required columns: supplier_code",
                  },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center space-x-4 p-3 border rounded-lg">
                    <div className="flex-shrink-0">
                      {activity.status === "success" && <CheckCircle className="h-5 w-5 text-green-500" />}
                      {activity.status === "processing" && <AlertTriangle className="h-5 w-5 text-yellow-500" />}
                      {activity.status === "error" && <XCircle className="h-5 w-5 text-red-500" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{activity.action}</p>
                        <span className="text-xs text-muted-foreground">{activity.time}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">{activity.details}</p>
                    </div>
                    <Badge
                      variant={
                        activity.status === "success"
                          ? "default"
                          : activity.status === "error"
                            ? "destructive"
                            : "secondary"
                      }
                    >
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
