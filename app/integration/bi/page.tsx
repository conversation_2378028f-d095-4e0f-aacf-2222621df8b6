"use client"

import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CheckCircle, Settings, RefreshCw, Database } from "lucide-react"

export default function BIConnectionPage() {
  const biConnections = [
    {
      id: 1,
      name: "Tomoro BI Production",
      environment: "Production",
      status: "connected",
      lastSync: "2024-01-15 11:15",
      endpoint: "https://bi.tomoro.com/api/v1",
    },
    {
      id: 2,
      name: "Tomoro BI Staging",
      environment: "Staging",
      status: "connected",
      lastSync: "2024-01-15 10:45",
      endpoint: "https://staging-bi.tomoro.com/api/v1",
    },
  ]

  const syncStats = [
    { label: "Data Penjualan", count: "1,234", status: "success" },
    { label: "Data Inventory", count: "567", status: "success" },
    { label: "Data Customer", count: "890", status: "pending" },
    { label: "Data Transaksi", count: "2,345", status: "success" },
  ]

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Koneksi BI Tomoro</h1>
              <p className="text-muted-foreground">
                Kelola koneksi ke sistem Business Intelligence Tomoro untuk analisis data
              </p>
            </div>
            <Button>
              <RefreshCw className="h-4 w-4 mr-2" />
              Sync Manual
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {syncStats.map((stat, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.label}</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.count}</div>
                  <Badge variant={stat.status === "success" ? "default" : "secondary"} className="mt-2">
                    {stat.status === "success" ? "Tersinkron" : "Pending"}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>

          <Tabs defaultValue="connections" className="space-y-4">
            <TabsList>
              <TabsTrigger value="connections">Koneksi BI</TabsTrigger>
              <TabsTrigger value="data-mapping">Mapping Data</TabsTrigger>
              <TabsTrigger value="settings">Pengaturan</TabsTrigger>
            </TabsList>

            <TabsContent value="connections" className="space-y-4">
              <div className="grid gap-4">
                {biConnections.map((connection) => (
                  <Card key={connection.id}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div className="space-y-1">
                        <CardTitle className="text-base">{connection.name}</CardTitle>
                        <CardDescription>{connection.environment}</CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="default">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Terhubung
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Terakhir Sinkron</p>
                          <p className="font-medium">{connection.lastSync}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Endpoint</p>
                          <p className="font-medium text-xs">{connection.endpoint}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="data-mapping" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Mapping Data BI</CardTitle>
                  <CardDescription>Konfigurasi mapping field data ke sistem BI Tomoro</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Field Sumber</Label>
                        <Input placeholder="transaction_amount" />
                      </div>
                      <div className="space-y-2">
                        <Label>Field Target BI</Label>
                        <Input placeholder="sales_amount" />
                      </div>
                    </div>
                    <Button>Tambah Mapping</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pengaturan Sinkronisasi BI</CardTitle>
                  <CardDescription>Konfigurasi sinkronisasi data ke sistem BI</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Real-time Sync</Label>
                      <p className="text-sm text-muted-foreground">Sinkronisasi data secara real-time</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="batch-size">Batch Size</Label>
                    <Input id="batch-size" type="number" defaultValue="100" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="api-timeout">API Timeout (detik)</Label>
                    <Input id="api-timeout" type="number" defaultValue="30" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
