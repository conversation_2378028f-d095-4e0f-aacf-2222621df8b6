import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Database, TestTube, Settings, CheckCircle, XCircle, Key, Globe } from "lucide-react"

export default function AccurateIntegration() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Accurate Integration</h1>
              <p className="text-muted-foreground">Konfigurasi API koneksi ke sistem Accurate</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <TestTube className="w-4 h-4 mr-2" />
                Test API
              </Button>
              <Button size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Save Configuration
              </Button>
            </div>
          </div>

          {/* API Status */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Status</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Connected</div>
                <p className="text-xs text-muted-foreground">Response time: 245ms</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Database</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">TOMORO_BEI</div>
                <p className="text-xs text-muted-foreground">Active database</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Today's Posts</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">47</div>
                <p className="text-xs text-muted-foreground">documents posted</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Quota</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,847</div>
                <p className="text-xs text-muted-foreground">calls remaining</p>
              </CardContent>
            </Card>
          </div>

          {/* Configuration */}
          <Tabs defaultValue="connection" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="connection">Connection</TabsTrigger>
              <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
              <TabsTrigger value="posting">Posting Rules</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TabsList>

            <TabsContent value="connection" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>API Connection Settings</CardTitle>
                  <CardDescription>Konfigurasi koneksi API ke Accurate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>API Base URL</Label>
                      <Input placeholder="https://api.accurate.id/api/v1/" />
                    </div>
                    <div className="space-y-2">
                      <Label>Database ID</Label>
                      <Input placeholder="12345" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>API Key</Label>
                    <div className="flex space-x-2">
                      <Input type="password" placeholder="••••••••••••••••••••••••••••••••" className="flex-1" />
                      <Button variant="outline" size="sm">
                        <Key className="w-4 h-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">API key akan dienkripsi dan disimpan dengan aman</p>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Timeout (seconds)</Label>
                      <Input type="number" defaultValue="30" />
                    </div>
                    <div className="space-y-2">
                      <Label>Max Retries</Label>
                      <Input type="number" defaultValue="3" />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="ssl-verify" defaultChecked />
                    <Label htmlFor="ssl-verify">Verify SSL certificates</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="endpoints" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>API Endpoints Configuration</CardTitle>
                  <CardDescription>Konfigurasi endpoint untuk berbagai operasi</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {[
                    { name: "Purchase Order", endpoint: "/purchase-order", method: "POST", status: "active" },
                    { name: "Goods Receipt", endpoint: "/goods-receipt", method: "POST", status: "active" },
                    { name: "Purchase Invoice", endpoint: "/purchase-invoice", method: "POST", status: "active" },
                    { name: "Sales Invoice", endpoint: "/sales-invoice", method: "POST", status: "active" },
                    { name: "Journal Entry", endpoint: "/journal-entry", method: "POST", status: "active" },
                    { name: "Chart of Accounts", endpoint: "/coa", method: "GET", status: "active" },
                  ].map((endpoint, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Badge variant="outline">{endpoint.method}</Badge>
                        <div>
                          <p className="font-medium">{endpoint.name}</p>
                          <p className="text-sm text-muted-foreground">{endpoint.endpoint}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={endpoint.status === "active" ? "default" : "secondary"}>
                          {endpoint.status}
                        </Badge>
                        <Button variant="ghost" size="sm">
                          <TestTube className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="posting" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Posting Rules</CardTitle>
                  <CardDescription>Aturan untuk posting dokumen ke Accurate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-base font-medium">Batch Processing</Label>
                      <div className="grid gap-4 md:grid-cols-2 mt-2">
                        <div className="space-y-2">
                          <Label>Batch Size</Label>
                          <Input type="number" defaultValue="50" />
                          <p className="text-xs text-muted-foreground">Jumlah dokumen per batch</p>
                        </div>
                        <div className="space-y-2">
                          <Label>Batch Interval (seconds)</Label>
                          <Input type="number" defaultValue="5" />
                          <p className="text-xs text-muted-foreground">Jeda antar batch</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-base font-medium">Document Numbering</Label>
                      <div className="grid gap-4 md:grid-cols-2 mt-2">
                        <div className="space-y-2">
                          <Label>Purchase Prefix</Label>
                          <Input defaultValue="PO-" />
                        </div>
                        <div className="space-y-2">
                          <Label>Sales Prefix</Label>
                          <Input defaultValue="SI-" />
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch id="auto-approve" />
                      <Label htmlFor="auto-approve">Auto-approve posted documents</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch id="idempotency" defaultChecked />
                      <Label htmlFor="idempotency">Enable idempotency keys</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="monitoring" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>API Monitoring</CardTitle>
                  <CardDescription>Monitor performa dan kesehatan API</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Health Check Interval (minutes)</Label>
                      <Input type="number" defaultValue="5" />
                    </div>
                    <div className="space-y-2">
                      <Label>Alert Threshold (ms)</Label>
                      <Input type="number" defaultValue="5000" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Webhook URL (Optional)</Label>
                    <Input placeholder="https://your-webhook-url.com/accurate-alerts" />
                    <p className="text-xs text-muted-foreground">URL untuk menerima notifikasi error atau downtime</p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="detailed-logs" defaultChecked />
                    <Label htmlFor="detailed-logs">Enable detailed API logs</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="performance-metrics" defaultChecked />
                    <Label htmlFor="performance-metrics">Collect performance metrics</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Recent API Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent API Activity</CardTitle>
              <CardDescription>Log aktivitas API terbaru</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    time: "11:45 AM",
                    action: "POST /sales-invoice",
                    status: "success",
                    details: "Batch of 25 invoices posted successfully",
                    responseTime: "342ms",
                  },
                  {
                    time: "11:30 AM",
                    action: "POST /purchase-order",
                    status: "success",
                    details: "12 purchase orders created",
                    responseTime: "198ms",
                  },
                  {
                    time: "11:15 AM",
                    action: "GET /coa",
                    status: "success",
                    details: "Chart of accounts synchronized",
                    responseTime: "156ms",
                  },
                  {
                    time: "11:00 AM",
                    action: "POST /journal-entry",
                    status: "error",
                    details: "Invalid COA mapping for account 4001",
                    responseTime: "2.1s",
                  },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center space-x-4 p-3 border rounded-lg">
                    <div className="flex-shrink-0">
                      {activity.status === "success" && <CheckCircle className="h-5 w-5 text-green-500" />}
                      {activity.status === "error" && <XCircle className="h-5 w-5 text-red-500" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{activity.action}</p>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-muted-foreground">{activity.responseTime}</span>
                          <span className="text-xs text-muted-foreground">{activity.time}</span>
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">{activity.details}</p>
                    </div>
                    <Badge variant={activity.status === "success" ? "default" : "destructive"}>{activity.status}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
