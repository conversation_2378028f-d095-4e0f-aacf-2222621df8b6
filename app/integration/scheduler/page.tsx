import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Clock, Play, Pause, Settings, CheckCircle, XCircle, Calendar } from "lucide-react"

const scheduledJobs = [
  {
    id: "1",
    name: "BOH Data Sync",
    description: "Sync purchase orders and receipts from BOH",
    schedule: "Daily at 09:00",
    status: "active",
    lastRun: "2024-12-15 09:00:00",
    nextRun: "2024-12-16 09:00:00",
    success: true,
  },
  {
    id: "2",
    name: "BI Sales Import",
    description: "Import sales data from BI Tomoro",
    schedule: "Every 4 hours",
    status: "active",
    lastRun: "2024-12-15 12:00:00",
    nextRun: "2024-12-15 16:00:00",
    success: true,
  },
  {
    id: "3",
    name: "Bank Reconciliation",
    description: "Auto-reconcile bank statements",
    schedule: "Daily at 17:00",
    status: "paused",
    lastRun: "2024-12-14 17:00:00",
    nextRun: "Paused",
    success: false,
  },
  {
    id: "4",
    name: "Accurate Posting",
    description: "Post validated transactions to Accurate",
    schedule: "Every 2 hours",
    status: "active",
    lastRun: "2024-12-15 14:00:00",
    nextRun: "2024-12-15 16:00:00",
    success: true,
  },
]

export default function SchedulerPage() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Job Scheduler</h1>
              <p className="text-muted-foreground">Kelola jadwal otomatis untuk semua proses integrasi</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Calendar className="w-4 h-4 mr-2" />
                Add Job
              </Button>
              <Button size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Global Settings
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">running scheduled jobs</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">98.5%</div>
                <p className="text-xs text-muted-foreground">last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Next Run</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">16:00</div>
                <p className="text-xs text-muted-foreground">BI Sales Import</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed Jobs</CardTitle>
                <XCircle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">needs attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Global Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Global Scheduler Settings</CardTitle>
              <CardDescription>Pengaturan umum untuk semua scheduled jobs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label>Timezone</Label>
                  <Select defaultValue="asia-jakarta">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asia-jakarta">Asia/Jakarta (WIB)</SelectItem>
                      <SelectItem value="utc">UTC</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Max Concurrent Jobs</Label>
                  <Input type="number" defaultValue="5" />
                </div>
                <div className="space-y-2">
                  <Label>Job Timeout (minutes)</Label>
                  <Input type="number" defaultValue="60" />
                </div>
              </div>

              <div className="flex items-center space-x-4 mt-4">
                <div className="flex items-center space-x-2">
                  <Switch id="scheduler-enabled" defaultChecked />
                  <Label htmlFor="scheduler-enabled">Enable scheduler</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="email-notifications" defaultChecked />
                  <Label htmlFor="email-notifications">Email notifications</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="detailed-logging" />
                  <Label htmlFor="detailed-logging">Detailed logging</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scheduled Jobs */}
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Jobs</CardTitle>
              <CardDescription>Daftar semua job yang dijadwalkan</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scheduledJobs.map((job) => (
                  <div key={job.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {job.success ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium">{job.name}</h4>
                          <p className="text-sm text-muted-foreground">{job.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={job.status === "active" ? "default" : "secondary"}>{job.status}</Badge>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          {job.status === "active" ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-4 text-sm">
                      <div>
                        <Label className="text-xs text-muted-foreground">Schedule</Label>
                        <p className="font-medium">{job.schedule}</p>
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Last Run</Label>
                        <p className="font-medium">{job.lastRun}</p>
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Next Run</Label>
                        <p className="font-medium">{job.nextRun}</p>
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Status</Label>
                        <p className={`font-medium ${job.success ? "text-green-600" : "text-red-600"}`}>
                          {job.success ? "Success" : "Failed"}
                        </p>
                      </div>
                    </div>

                    {/* Job Configuration */}
                    <div className="mt-4 pt-4 border-t">
                      <div className="grid gap-4 md:grid-cols-4">
                        <div className="space-y-2">
                          <Label className="text-sm">Frequency</Label>
                          <Select defaultValue="daily">
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="hourly">Hourly</SelectItem>
                              <SelectItem value="daily">Daily</SelectItem>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="custom">Custom</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Time</Label>
                          <Input type="time" defaultValue="09:00" className="h-8" />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Max Retries</Label>
                          <Input type="number" defaultValue="3" className="h-8" />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Priority</Label>
                          <Select defaultValue="normal">
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="normal">Normal</SelectItem>
                              <SelectItem value="low">Low</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Job Execution History */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Job Executions</CardTitle>
              <CardDescription>History eksekusi job dalam 24 jam terakhir</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    time: "14:00:00",
                    job: "Accurate Posting",
                    duration: "2.3s",
                    status: "success",
                    details: "Posted 23 transactions successfully",
                  },
                  {
                    time: "12:00:00",
                    job: "BI Sales Import",
                    duration: "45.2s",
                    status: "success",
                    details: "Imported 156 sales records",
                  },
                  {
                    time: "09:00:00",
                    job: "BOH Data Sync",
                    duration: "1m 23s",
                    status: "success",
                    details: "Synced 89 purchase orders",
                  },
                  {
                    time: "08:30:00",
                    job: "Bank Reconciliation",
                    duration: "timeout",
                    status: "failed",
                    details: "Connection timeout to bank API",
                  },
                ].map((execution, index) => (
                  <div key={index} className="flex items-center space-x-4 p-3 border rounded-lg">
                    <div className="flex-shrink-0">
                      {execution.status === "success" && <CheckCircle className="h-5 w-5 text-green-500" />}
                      {execution.status === "failed" && <XCircle className="h-5 w-5 text-red-500" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{execution.job}</p>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-muted-foreground">{execution.duration}</span>
                          <span className="text-xs text-muted-foreground">{execution.time}</span>
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">{execution.details}</p>
                    </div>
                    <Badge variant={execution.status === "success" ? "default" : "destructive"}>
                      {execution.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
