"use client"

import { Sidebar } from "@/components/sidebar"
import { FileUpload } from "@/components/file-upload"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowRight, TrendingUp, Calendar, FileText, CheckCircle } from "lucide-react"

const recentUploads = [
  {
    id: 1,
    filename: "BI_Tomoro_20240315_AllChannel.csv",
    uploadTime: "2024-03-15 08:30:00",
    channels: ["GoFood", "GrabFood", "ShopeeFood"],
    transactions: 156,
    totalGross: 22000000,
    status: "processed",
  },
  {
    id: 2,
    filename: "BI_Tomoro_20240314_AllChannel.csv",
    uploadTime: "2024-03-14 17:45:00",
    channels: ["GoFood", "GrabFood"],
    transactions: 134,
    totalGross: 18500000,
    status: "processed",
  },
  {
    id: 3,
    filename: "BI_Tomoro_20240313_AllChannel.csv",
    uploadTime: "2024-03-13 09:15:00",
    channels: ["GoFood", "GrabFood", "ShopeeFood", "Offline"],
    transactions: 189,
    totalGross: 25300000,
    status: "processing",
  },
]

export default function UploadBI() {
  const handleFileUpload = (files: File[]) => {
    console.log("BI files uploaded:", files)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Upload Penjualan BI</h1>
              <p className="text-muted-foreground">Upload data penjualan dari BI Tomoro (All Channel, per tanggal)</p>
            </div>
            <Button>
              <ArrowRight className="w-4 h-4 mr-2" />
              Lanjut ke Upload Bank
            </Button>
          </div>

          {/* Instructions */}
          <Alert>
            <FileText className="h-4 w-4" />
            <AlertDescription>
              Upload file ekspor penjualan dari BI Tomoro dalam format CSV/Excel. File harus berisi data: tanggal,
              channel, gross sales, diskon/fee/ads, nett sales, dan detail item per transaksi.
            </AlertDescription>
          </Alert>

          {/* File Upload */}
          <FileUpload
            accept={{
              "text/csv": [".csv"],
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
              "application/vnd.ms-excel": [".xls"],
            }}
            onFileUpload={handleFileUpload}
          />

          {/* Processing Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Files Today</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">files uploaded</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Transactions</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">479</div>
                <p className="text-xs text-muted-foreground">total processed</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Channels</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4</div>
                <p className="text-xs text-muted-foreground">active channels</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Rp 65.8M</div>
                <p className="text-xs text-muted-foreground">gross sales</p>
              </CardContent>
            </Card>
          </div>

          {/* Data Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Data Preview</CardTitle>
              <CardDescription>Preview data yang berhasil diparse dari file BI</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Channel Breakdown</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>GoFood:</span>
                        <span className="font-medium">Rp 22.5M (45%)</span>
                      </div>
                      <div className="flex justify-between">
                        <span>GrabFood:</span>
                        <span className="font-medium">Rp 18.2M (37%)</span>
                      </div>
                      <div className="flex justify-between">
                        <span>ShopeeFood:</span>
                        <span className="font-medium">Rp 8.1M (16%)</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Offline:</span>
                        <span className="font-medium">Rp 1.0M (2%)</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Fee & Discount Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Total Commission:</span>
                        <span className="font-medium text-red-600">Rp -4.25M</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Ads:</span>
                        <span className="font-medium text-red-600">Rp -250K</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Net Sales:</span>
                        <span className="font-medium text-green-600">Rp 45.3M</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-green-800">Data Successfully Parsed</span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    Semua channel terdeteksi dengan benar. Diskon otomatis digenerate dari gross ke nett sales.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Uploads */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Uploads</CardTitle>
              <CardDescription>File BI yang baru saja diupload dan diproses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentUploads.map((upload) => (
                  <div key={upload.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <FileText className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{upload.filename}</p>
                        <p className="text-sm text-muted-foreground">{upload.uploadTime}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          {upload.channels.map((channel) => (
                            <Badge key={channel} variant="secondary" className="text-xs">
                              {channel}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{upload.transactions} transactions</p>
                      <p className="text-sm text-muted-foreground">
                        Rp {(upload.totalGross / 1000000).toFixed(1)}M gross
                      </p>
                      <Badge variant={upload.status === "processed" ? "default" : "secondary"} className="mt-1">
                        {upload.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
