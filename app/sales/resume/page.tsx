import { Sidebar } from "@/components/sidebar"
import { ReconciliationResume } from "@/components/reconciliation-resume"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, CheckCircle, Send, Download, FileText } from "lucide-react"

const postingHistory = [
  {
    id: 1,
    date: "2024-03-14",
    batchId: "SALES-2024-0314",
    channels: 3,
    totalAmount: 18500000,
    status: "posted",
    accurateDoc: "SJ-2024-0314",
  },
  {
    id: 2,
    date: "2024-03-13",
    batchId: "SALES-2024-0313",
    channels: 4,
    totalAmount: 25300000,
    status: "posted",
    accurateDoc: "SJ-2024-0313",
  },
  {
    id: 3,
    date: "2024-03-12",
    batchId: "SALES-2024-0312",
    channels: 2,
    totalAmount: 12800000,
    status: "failed",
    accurateDoc: "",
  },
]

export default function ResumeRekonsiliasi() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Resume Rekonsiliasi</h1>
              <p className="text-muted-foreground">Laporan final rekonsiliasi untuk posting ke Accurate</p>
            </div>
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali ke Dashboard
            </Button>
          </div>

          {/* Status Alert */}
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Rekonsiliasi selesai untuk tanggal 15 Maret 2024. Total penjualan diakui: Rp 14.950.000 dengan selisih Rp
              -50.000.
            </AlertDescription>
          </Alert>

          {/* Main Resume Component */}
          <ReconciliationResume />

          {/* Additional Reports */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Item Detail Report</CardTitle>
                <CardDescription>Breakdown penjualan per item menu</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Kopi Americano</p>
                      <p className="text-sm text-muted-foreground">45 cups sold</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Rp 675.000</p>
                      <p className="text-xs text-muted-foreground">avg: Rp 15.000</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Cappuccino</p>
                      <p className="text-sm text-muted-foreground">38 cups sold</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Rp 760.000</p>
                      <p className="text-xs text-muted-foreground">avg: Rp 20.000</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Latte</p>
                      <p className="text-sm text-muted-foreground">52 cups sold</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Rp 1.040.000</p>
                      <p className="text-xs text-muted-foreground">avg: Rp 20.000</p>
                    </div>
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-4 bg-transparent">
                  <FileText className="w-4 h-4 mr-2" />
                  View Full Item Report
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Accurate Posting Preview</CardTitle>
                <CardDescription>Preview format yang akan diposting ke Accurate</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <div className="font-medium mb-2">Journal Entry Preview:</div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>Dr. Bank BCA</span>
                        <span>Rp 14.950.000</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Dr. Selisih Bank</span>
                        <span>Rp 50.000</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cr. Sales - GoFood</span>
                        <span>Rp 7.950.000</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cr. Sales - GrabFood</span>
                        <span>Rp 7.050.000</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="font-medium mb-2">Document Details:</div>
                    <div className="space-y-1 text-xs">
                      <div>Document No: SJ-2024-0315</div>
                      <div>Date: 15/03/2024</div>
                      <div>Reference: BI-TOMORO-********</div>
                      <div>Description: Sales Reconciliation</div>
                    </div>
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-4 bg-transparent">
                  <Send className="w-4 h-4 mr-2" />
                  Preview in Accurate
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Posting History */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Posting History</CardTitle>
              <CardDescription>History posting rekonsiliasi ke Accurate</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {postingHistory.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{item.batchId}</p>
                        <p className="text-sm text-muted-foreground">{item.date}</p>
                      </div>
                      <div className="text-sm">
                        <p>{item.channels} channels</p>
                        <p className="text-muted-foreground">Rp {(item.totalAmount / 1000000).toFixed(1)}M</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right text-sm">
                        {item.accurateDoc && <p className="font-medium">{item.accurateDoc}</p>}
                        <Badge variant={item.status === "posted" ? "default" : "destructive"}>{item.status}</Badge>
                      </div>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Final Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Final Actions</CardTitle>
              <CardDescription>Complete the reconciliation process</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium">Ready to post to Accurate</p>
                  <p className="text-sm text-muted-foreground">
                    All reconciliation steps completed. Review summary before final posting.
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Export Report
                  </Button>
                  <Button>
                    <Send className="w-4 h-4 mr-2" />
                    Post to Accurate
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
