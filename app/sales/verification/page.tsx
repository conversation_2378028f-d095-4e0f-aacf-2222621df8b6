import { Sidebar } from "@/components/sidebar"
import { AIMatchingEngine } from "@/components/ai-matching-engine"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, ArrowRight, Eye, Edit, CheckCircle } from "lucide-react"

const biData = [
  {
    id: 1,
    channel: "GoFood",
    date: "2024-03-15",
    grossSales: ********,
    comFee: 2000000,
    ads: 50000,
    nettSales: 7950000,
    transactions: 45,
  },
  {
    id: 2,
    channel: "GrabFood",
    date: "2024-03-15",
    grossSales: 9000000,
    comFee: 1800000,
    ads: 150000,
    nettSales: 7050000,
    transactions: 38,
  },
  {
    id: 3,
    channel: "ShopeeFood",
    date: "2024-03-15",
    grossSales: 3000000,
    comFee: 450000,
    ads: 50000,
    nettSales: 2500000,
    transactions: 22,
  },
]

const bankData = [
  {
    id: 1,
    date: "2024-03-16",
    description: "GOFOOD-MERCHANT-12345",
    amount: 7900000,
    channel: "GoFood",
    confidence: 95,
    status: "matched",
  },
  {
    id: 2,
    date: "2024-03-16",
    description: "GRAB-FOOD-67890",
    amount: 7050000,
    channel: "GrabFood",
    confidence: 100,
    status: "matched",
  },
  {
    id: 3,
    date: "2024-03-16",
    description: "TRANSFER-UNKNOWN",
    amount: 500000,
    channel: "Unknown",
    confidence: 0,
    status: "unmatched",
  },
]

const matchingResults = [
  {
    id: 1,
    channel: "GoFood",
    biAmount: 7950000,
    bankAmount: 7900000,
    difference: -50000,
    status: "partial",
    rule: "T vs T+1",
    confidence: 95,
  },
  {
    id: 2,
    channel: "GrabFood",
    biAmount: 7050000,
    bankAmount: 7050000,
    difference: 0,
    status: "matched",
    rule: "T vs T+1",
    confidence: 100,
  },
  {
    id: 3,
    channel: "ShopeeFood",
    biAmount: 2500000,
    bankAmount: 0,
    difference: -2500000,
    status: "unmatched",
    rule: "T vs T+1",
    confidence: 0,
  },
]

export default function MatchVerification() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Match & Verifikasi</h1>
              <p className="text-muted-foreground">AI-powered matching dan verifikasi transaksi BI vs Bank</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button>
                <ArrowRight className="w-4 h-4 mr-2" />
                Lanjut ke Review
              </Button>
            </div>
          </div>

          {/* Matching Rules */}
          <Card>
            <CardHeader>
              <CardTitle>Matching Rules Configuration</CardTitle>
              <CardDescription>Konfigurasi aturan matching untuk setiap channel</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date Matching Rule</label>
                  <Select defaultValue="t-plus-1">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="same-day">Same Day (T vs T)</SelectItem>
                      <SelectItem value="t-plus-1">Next Day (T vs T+1)</SelectItem>
                      <SelectItem value="t-plus-2">T+2 Days</SelectItem>
                      <SelectItem value="flexible">Flexible (±3 days)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Tolerance Amount</label>
                  <Select defaultValue="50000">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Exact Match</SelectItem>
                      <SelectItem value="10000">Rp 10.000</SelectItem>
                      <SelectItem value="50000">Rp 50.000</SelectItem>
                      <SelectItem value="100000">Rp 100.000</SelectItem>
                      <SelectItem value="percentage">5% Tolerance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Channel Priority</label>
                  <Select defaultValue="bi-priority">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bi-priority">BI Data Priority</SelectItem>
                      <SelectItem value="bank-priority">Bank Data Priority</SelectItem>
                      <SelectItem value="manual-review">Manual Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI Matching Engine */}
          <AIMatchingEngine />

          {/* Data Comparison Tabs */}
          <Tabs defaultValue="comparison" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="comparison">Side by Side</TabsTrigger>
              <TabsTrigger value="bi-data">BI Data</TabsTrigger>
              <TabsTrigger value="bank-data">Bank Data</TabsTrigger>
              <TabsTrigger value="results">Matching Results</TabsTrigger>
            </TabsList>

            <TabsContent value="comparison" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>BI Tomoro Data</CardTitle>
                    <CardDescription>Data penjualan dari BI per channel</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {biData.map((item) => (
                        <div key={item.id} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{item.channel}</h4>
                            <Badge variant="secondary">{item.transactions} txn</Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">Gross:</span>
                              <span className="ml-2 font-medium">Rp {(item.grossSales / 1000000).toFixed(1)}M</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Nett:</span>
                              <span className="ml-2 font-medium text-green-600">
                                Rp {(item.nettSales / 1000000).toFixed(1)}M
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Bank Data</CardTitle>
                    <CardDescription>Transaksi masuk dari rekening koran</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {bankData.map((item) => (
                        <div key={item.id} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{item.channel}</h4>
                            <Badge
                              variant={
                                item.status === "matched"
                                  ? "default"
                                  : item.status === "unmatched"
                                    ? "destructive"
                                    : "secondary"
                              }
                            >
                              {item.status}
                            </Badge>
                          </div>
                          <div className="text-sm">
                            <p className="text-muted-foreground">{item.description}</p>
                            <p className="font-medium">Rp {item.amount.toLocaleString("id-ID")}</p>
                            <p className="text-xs text-muted-foreground">{item.date}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Matching Results</CardTitle>
                  <CardDescription>Hasil matching AI dengan confidence score</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {matchingResults.map((result) => (
                      <div key={result.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium">{result.channel}</h4>
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="text-xs">
                              {result.confidence}% confidence
                            </Badge>
                            <Badge
                              variant={
                                result.status === "matched"
                                  ? "default"
                                  : result.status === "partial"
                                    ? "secondary"
                                    : "destructive"
                              }
                            >
                              {result.status}
                            </Badge>
                          </div>
                        </div>

                        <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                          <div>
                            <span className="text-muted-foreground">BI Amount:</span>
                            <div className="font-medium">Rp {result.biAmount.toLocaleString("id-ID")}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Bank Amount:</span>
                            <div className="font-medium">Rp {result.bankAmount.toLocaleString("id-ID")}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Difference:</span>
                            <div className={`font-medium ${result.difference < 0 ? "text-red-600" : "text-green-600"}`}>
                              Rp {result.difference.toLocaleString("id-ID")}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">Rule: {result.rule}</span>
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4 mr-2" />
                              Details
                            </Button>
                            {result.status !== "matched" && (
                              <Button size="sm">
                                <Edit className="w-4 h-4 mr-2" />
                                Adjust
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Verification Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Verification Summary</CardTitle>
              <CardDescription>Ringkasan hasil verifikasi untuk approval</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3 mb-4">
                <div className="p-3 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">1</div>
                  <div className="text-sm text-muted-foreground">Perfect Match</div>
                </div>
                <div className="p-3 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-600">1</div>
                  <div className="text-sm text-muted-foreground">Partial Match</div>
                </div>
                <div className="p-3 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-red-600">1</div>
                  <div className="text-sm text-muted-foreground">Unmatched</div>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>AI matching completed with 87% average confidence</span>
                </div>
                <Button>Approve Verification Results</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
