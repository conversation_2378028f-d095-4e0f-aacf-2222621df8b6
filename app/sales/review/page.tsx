import { Sidebar } from "@/components/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, ArrowRight, AlertTriangle, CheckCircle, Edit, MessageSquare } from "lucide-react"

const selisihData = [
  {
    id: 1,
    channel: "GoFood",
    biAmount: 7950000,
    bankAmount: 7900000,
    selisih: -50000,
    percentage: -0.63,
    possibleCauses: ["Fee belum dipotong", "Rounding difference", "Pending adjustment"],
    priority: "low",
    status: "pending",
    notes: "",
  },
  {
    id: 2,
    channel: "ShopeeFood",
    biAmount: 2500000,
    bankAmount: 0,
    selisih: -2500000,
    percentage: -100,
    possibleCauses: ["Pending payout", "Weekend delay", "Bank holiday"],
    priority: "high",
    status: "pending",
    notes: "",
  },
  {
    id: 3,
    channel: "Unknown Transaction",
    biAmount: 0,
    bankAmount: 500000,
    selisih: 500000,
    percentage: 0,
    possibleCauses: ["Refund", "Manual transfer", "Other income"],
    priority: "medium",
    status: "pending",
    notes: "",
  },
]

export default function ReviewSelisih() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Review Selisih</h1>
              <p className="text-muted-foreground">Review dan kategorisasi selisih untuk tindakan yang tepat</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button>
                <ArrowRight className="w-4 h-4 mr-2" />
                Lanjut ke Resume
              </Button>
            </div>
          </div>

          {/* Summary Alert */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Ditemukan 3 item dengan selisih yang memerlukan review. Total selisih: Rp -2.050.000. Prioritas tinggi: 1
              item.
            </AlertDescription>
          </Alert>

          {/* Priority Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Selisih</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">Rp -2.05M</div>
                <p className="text-xs text-muted-foreground">needs attention</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">High Priority</CardTitle>
                <AlertTriangle className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">critical items</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Medium Priority</CardTitle>
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">review needed</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Low Priority</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">minor issues</p>
              </CardContent>
            </Card>
          </div>

          {/* Selisih Review Items */}
          <div className="space-y-6">
            {selisihData.map((item) => (
              <Card key={item.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{item.channel}</CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={
                          item.priority === "high"
                            ? "destructive"
                            : item.priority === "medium"
                              ? "secondary"
                              : "default"
                        }
                      >
                        {item.priority} priority
                      </Badge>
                      <Badge variant="outline">{item.status}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Amount Details */}
                  <div className="grid grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">BI Amount</div>
                      <div className="font-medium">Rp {item.biAmount.toLocaleString("id-ID")}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">Bank Amount</div>
                      <div className="font-medium">Rp {item.bankAmount.toLocaleString("id-ID")}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">Selisih</div>
                      <div className={`font-bold ${item.selisih < 0 ? "text-red-600" : "text-green-600"}`}>
                        Rp {item.selisih.toLocaleString("id-ID")}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">Percentage</div>
                      <div className={`font-medium ${item.percentage < 0 ? "text-red-600" : "text-green-600"}`}>
                        {item.percentage.toFixed(2)}%
                      </div>
                    </div>
                  </div>

                  {/* Possible Causes */}
                  <div>
                    <Label className="text-sm font-medium">Possible Causes (AI Analysis)</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {item.possibleCauses.map((cause, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {cause}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Action Selection */}
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor={`action-${item.id}`}>Recommended Action</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select action..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="accept-bi">Accept BI Data (Default)</SelectItem>
                          <SelectItem value="accept-bank">Accept Bank Data</SelectItem>
                          <SelectItem value="manual-adjustment">Manual Adjustment</SelectItem>
                          <SelectItem value="investigate">Need Investigation</SelectItem>
                          <SelectItem value="ignore">Ignore (Known Issue)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`category-${item.id}`}>Category</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fee-timing">Fee Timing Difference</SelectItem>
                          <SelectItem value="payout-delay">Payout Delay</SelectItem>
                          <SelectItem value="rounding">Rounding Difference</SelectItem>
                          <SelectItem value="refund">Refund/Return</SelectItem>
                          <SelectItem value="other-income">Other Income</SelectItem>
                          <SelectItem value="system-error">System Error</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Notes */}
                  <div className="space-y-2">
                    <Label htmlFor={`notes-${item.id}`}>Review Notes</Label>
                    <Textarea
                      id={`notes-${item.id}`}
                      placeholder="Add notes about this selisih..."
                      className="min-h-[80px]"
                    />
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">Last updated: Never</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Details
                      </Button>
                      <Button size="sm">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Approve Resolution
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Bulk Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Bulk Actions</CardTitle>
              <CardDescription>Apply actions to multiple selisih items</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Select>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Select action..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="accept-all-bi">Accept All BI Data</SelectItem>
                      <SelectItem value="mark-reviewed">Mark All as Reviewed</SelectItem>
                      <SelectItem value="export-report">Export Review Report</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline">Apply to Selected</Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline">Save Progress</Button>
                  <Button>Complete Review</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
