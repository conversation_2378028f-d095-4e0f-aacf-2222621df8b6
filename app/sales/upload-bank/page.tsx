"use client"

import { Sidebar } from "@/components/sidebar"
import { FileUpload } from "@/components/file-upload"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { ArrowLeft, ArrowRight, Banknote, Wifi, FileText, AlertTriangle } from "lucide-react"

const bankAccounts = [
  {
    id: 1,
    bank: "BCA",
    accountNumber: "**********",
    accountName: "PT Tomoro Coffee BEI",
    lastSync: "2024-03-15 07:00:00",
    status: "connected",
    apiEnabled: true,
  },
  {
    id: 2,
    bank: "Mandiri",
    accountNumber: "**********",
    accountName: "PT Tomoro Coffee Lotte",
    lastSync: "2024-03-15 07:00:00",
    status: "connected",
    apiEnabled: true,
  },
  {
    id: 3,
    bank: "BNI",
    accountNumber: "**********",
    accountName: "PT Tomoro Coffee Central",
    lastSync: "Manual Upload",
    status: "manual",
    apiEnabled: false,
  },
]

const recentTransactions = [
  {
    id: 1,
    date: "2024-03-15",
    description: "GOFOOD-MERCHANT-12345",
    amount: 7900000,
    type: "credit",
    channel: "GoFood",
    matched: true,
  },
  {
    id: 2,
    date: "2024-03-15",
    description: "GRAB-FOOD-67890",
    amount: 7050000,
    type: "credit",
    channel: "GrabFood",
    matched: true,
  },
  {
    id: 3,
    date: "2024-03-15",
    description: "TRANSFER-UNKNOWN",
    amount: 500000,
    type: "credit",
    channel: "Unknown",
    matched: false,
  },
]

export default function UploadBank() {
  const handleFileUpload = (files: File[]) => {
    console.log("Bank files uploaded:", files)
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Upload Rekening Koran</h1>
              <p className="text-muted-foreground">Upload rekening koran atau gunakan API Bank untuk auto-sync</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button>
                <ArrowRight className="w-4 h-4 mr-2" />
                Lanjut ke Verifikasi
              </Button>
            </div>
          </div>

          {/* Bank API Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wifi className="h-5 w-5" />
                <span>Bank API Integration</span>
              </CardTitle>
              <CardDescription>Status koneksi API bank untuk auto-sync rekening koran</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bankAccounts.map((account) => (
                  <div key={account.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <Banknote className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">
                          {account.bank} - {account.accountNumber}
                        </p>
                        <p className="text-sm text-muted-foreground">{account.accountName}</p>
                        <p className="text-xs text-muted-foreground">Last sync: {account.lastSync}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge variant={account.status === "connected" ? "default" : "secondary"}>{account.status}</Badge>
                      <div className="flex items-center space-x-2">
                        <Switch checked={account.apiEnabled} />
                        <Label className="text-sm">Auto Sync</Label>
                      </div>
                      <Button variant="outline" size="sm">
                        {account.apiEnabled ? "Sync Now" : "Setup API"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Manual Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Manual Upload</CardTitle>
              <CardDescription>Upload file rekening koran untuk bank yang belum terintegrasi API</CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <FileText className="h-4 w-4" />
                <AlertDescription>
                  Upload file rekening koran dalam format CSV/Excel. File harus berisi kolom: tanggal, deskripsi,
                  debit/kredit, saldo.
                </AlertDescription>
              </Alert>

              <FileUpload
                accept={{
                  "text/csv": [".csv"],
                  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
                  "application/vnd.ms-excel": [".xls"],
                }}
                onFileUpload={handleFileUpload}
              />
            </CardContent>
          </Card>

          {/* Transaction Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Bank Transactions</CardTitle>
              <CardDescription>Preview transaksi bank yang berhasil diparse</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="text-sm font-medium">{transaction.date}</div>
                        <div className="text-xs text-muted-foreground">{transaction.type}</div>
                      </div>
                      <div>
                        <p className="font-medium">{transaction.description}</p>
                        <p className="text-sm text-muted-foreground">Channel: {transaction.channel}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Rp {transaction.amount.toLocaleString("id-ID")}</p>
                      <Badge variant={transaction.matched ? "default" : "destructive"} className="text-xs">
                        {transaction.matched ? "Matched" : "Unmatched"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>

              {recentTransactions.filter((t) => !t.matched).length > 0 && (
                <Alert className="mt-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Ditemukan {recentTransactions.filter((t) => !t.matched).length} transaksi yang belum ter-match
                    dengan channel. Review di tahap verifikasi.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Bank Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Inflow</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Rp 15.45M</div>
                <p className="text-xs text-muted-foreground">today</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Matched</CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">14.95M</div>
                <p className="text-xs text-muted-foreground">96.8% matched</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Unmatched</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">500K</div>
                <p className="text-xs text-muted-foreground">needs review</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Status</CardTitle>
                <Wifi className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2/3</div>
                <p className="text-xs text-muted-foreground">banks connected</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
