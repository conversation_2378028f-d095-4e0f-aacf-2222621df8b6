"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  LayoutDashboard,
  ShoppingCart,
  TrendingUp,
  Settings,
  Database,
  Calendar,
  FileText,
  Menu,
  Coffee,
  ChevronDown,
  ChevronRight,
} from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

const navigation = [
  {
    name: "Dashboard",
    href: "/",
    icon: LayoutDashboard,
  },
  {
    name: "Pembelian",
    icon: ShoppingCart,
    children: [
      { name: "Import BOH", href: "/purchase/import" },
      { name: "Transform & Preview", href: "/purchase/transform" },
      { name: "Mapping & Validasi", href: "/purchase/mapping" },
      { name: "Penerimaan", href: "/purchase/receiving" },
      { name: "Histori & Audit", href: "/purchase/history" },
    ],
  },
  {
    name: "Penjualan & Rekonsiliasi",
    icon: TrendingUp,
    children: [
      { name: "Upload Penjualan BI", href: "/sales/upload-bi" },
      { name: "Upload Rekening Koran", href: "/sales/upload-bank" },
      { name: "Match & Verifikasi", href: "/sales/verification" },
      { name: "Review Selisih", href: "/sales/review" },
      { name: "Resume Rekonsiliasi", href: "/sales/resume" },
    ],
  },
  {
    name: "Master & Mapping",
    icon: Database,
    children: [
      { name: "COA Mapping", href: "/master/coa" },
      { name: "Item/Product Mapping", href: "/master/items" },
      { name: "Channel Mapping", href: "/master/channels" },
      { name: "Bank Mapping", href: "/master/banks" },
      { name: "Tax & Fee Rules", href: "/master/tax-rules" },
    ],
  },
  {
    name: "Integrasi & Scheduler",
    icon: Calendar,
    children: [
      { name: "Koneksi BOH", href: "/integration/boh" },
      { name: "Koneksi BI Tomoro", href: "/integration/bi" },
      { name: "Koneksi Accurate", href: "/integration/accurate" },
      { name: "Koneksi Bank", href: "/integration/bank" },
      { name: "Scheduler Jobs", href: "/integration/scheduler" },
    ],
  },
  {
    name: "Audit Log & Notifikasi",
    icon: FileText,
    children: [
      { name: "Activity Logs", href: "/audit/logs" },
      { name: "Error Center", href: "/audit/errors" },
      { name: "Notifikasi", href: "/audit/notifications" },
    ],
  },
  {
    name: "Pengaturan",
    href: "/settings",
    icon: Settings,
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const [openItems, setOpenItems] = useState<string[]>([])

  const toggleItem = (name: string) => {
    setOpenItems((prev) => (prev.includes(name) ? prev.filter((item) => item !== name) : [...prev, name]))
  }

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      <div className="flex h-14 items-center border-b px-4">
        <Coffee className="mr-2 h-6 w-6 text-primary" />
        <span className="font-semibold text-lg">Tomoro Bridging</span>
      </div>
      <ScrollArea className="flex-1 px-3">
        <div className="space-y-2 py-4">
          {navigation.map((item) => {
            if (item.children) {
              const isOpen = openItems.includes(item.name)
              const hasActiveChild = item.children.some((child) => pathname === child.href)

              return (
                <Collapsible key={item.name} open={isOpen} onOpenChange={() => toggleItem(item.name)}>
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full justify-start",
                        (isOpen || hasActiveChild) && "bg-sidebar-accent text-sidebar-accent-foreground",
                      )}
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      {item.name}
                      {isOpen ? (
                        <ChevronDown className="ml-auto h-4 w-4" />
                      ) : (
                        <ChevronRight className="ml-auto h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-1">
                    {item.children.map((child) => (
                      <Button
                        key={child.href}
                        variant="ghost"
                        size="sm"
                        asChild
                        className={cn(
                          "w-full justify-start pl-8",
                          pathname === child.href && "bg-sidebar-primary text-sidebar-primary-foreground",
                        )}
                      >
                        <Link href={child.href}>{child.name}</Link>
                      </Button>
                    ))}
                  </CollapsibleContent>
                </Collapsible>
              )
            }

            return (
              <Button
                key={item.name}
                variant="ghost"
                asChild
                className={cn(
                  "w-full justify-start",
                  pathname === item.href && "bg-sidebar-primary text-sidebar-primary-foreground",
                )}
              >
                <Link href={item.href}>
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.name}
                </Link>
              </Button>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={cn("hidden border-r bg-sidebar lg:block lg:w-64", className)}>
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="lg:hidden bg-transparent">
            <Menu className="h-4 w-4" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>
    </>
  )
}
