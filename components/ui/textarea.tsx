import type * as React from "react"

import { cn } from "@/lib/utils"

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        "border-input placeholder:text-muted-foreground focus-visible:border-orange-500 focus-visible:ring-orange-500/20 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-white hover:bg-orange-50/30 border-2 border-gray-300 hover:border-orange-400 transition-all duration-200 flex field-sizing-content min-h-16 w-full rounded-lg px-3 py-2 text-base shadow-sm outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm resize-y",
        className,
      )}
      {...props}
    />
  )
}

export { Textarea }
