import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ShoppingCart, TrendingUp, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, DollarSign } from "lucide-react"

export function DashboardStats() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Purchase Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pembelian</CardTitle>
          <ShoppingCart className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">12</div>
          <p className="text-xs text-muted-foreground">dokumen siap posting</p>
          <div className="mt-2 flex items-center space-x-2">
            <Badge variant="destructive" className="text-xs">
              2 error
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Sales Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Penjualan</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">8</div>
          <p className="text-xs text-muted-foreground">batch menunggu bank</p>
          <div className="mt-2 flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              1 selisih {">"} 50k
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Success Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">94%</div>
          <p className="text-xs text-muted-foreground">transaksi berhasil</p>
          <Progress value={94} className="mt-2" />
        </CardContent>
      </Card>

      {/* Total Selisih */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Selisih</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">Rp 125k</div>
          <p className="text-xs text-muted-foreground">7 hari terakhir</p>
          <div className="mt-2 flex items-center space-x-2">
            <AlertTriangle className="h-3 w-3 text-yellow-500" />
            <span className="text-xs text-muted-foreground">Perlu review</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
