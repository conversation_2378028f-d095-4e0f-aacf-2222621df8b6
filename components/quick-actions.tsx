import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, Download, Send as Sync, FileText } from "lucide-react"

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>Aksi cepat untuk operasi harian</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Button className="h-20 flex-col space-y-2">
          <Upload className="h-6 w-6" />
          <span className="text-sm">Upload BI</span>
        </Button>
        <Button variant="outline" className="h-20 flex-col space-y-2 bg-transparent">
          <FileText className="h-6 w-6" />
          <span className="text-sm">Upload Bank</span>
        </Button>
        <Button variant="outline" className="h-20 flex-col space-y-2 bg-transparent">
          <Sync className="h-6 w-6" />
          <span className="text-sm">Sinkron BOH</span>
        </Button>
        <Button variant="outline" className="h-20 flex-col space-y-2 bg-transparent">
          <Download className="h-6 w-6" />
          <span className="text-sm">Download Report</span>
        </Button>
      </CardContent>
    </Card>
  )
}
